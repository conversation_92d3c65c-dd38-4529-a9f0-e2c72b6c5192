import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { Asset } from '../modules/assets/entities/asset.entity';
import { Category } from '../modules/categories/entities/category.entity';
import { Tag } from '../modules/tags/entities/tag.entity';
import { AssetVersion } from '../modules/versions/entities/asset-version.entity';
import { User } from '../modules/auth/entities/user.entity';

@Injectable()
export class DatabaseConfig {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    return {
      type: 'postgres',
      host: this.configService.get('DB_HOST', 'localhost'),
      port: this.configService.get('DB_PORT', 5432),
      username: this.configService.get('DB_USERNAME', 'postgres'),
      password: this.configService.get('DB_PASSWORD', 'password'),
      database: this.configService.get('DB_DATABASE', 'asset_library'),
      entities: [Asset, Category, Tag, AssetVersion, User],
      synchronize: this.configService.get('NODE_ENV') !== 'production',
      logging: this.configService.get('NODE_ENV') === 'development',
      ssl: this.configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
      migrations: ['dist/migrations/*.js'],
      migrationsRun: true,
      retryAttempts: 3,
      retryDelay: 3000,
      autoLoadEntities: true,
      keepConnectionAlive: true,
      extra: {
        max: this.configService.get('DB_MAX_CONNECTIONS', 20),
        min: this.configService.get('DB_MIN_CONNECTIONS', 5),
        acquire: 30000,
        idle: 10000,
      },
    };
  }
}
