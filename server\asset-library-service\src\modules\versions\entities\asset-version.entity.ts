import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { Asset } from '../../assets/entities/asset.entity';
import { User } from '../../auth/entities/user.entity';

export enum VersionStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  DEPRECATED = 'deprecated',
  DELETED = 'deleted',
}

@Entity('asset_versions')
@Index(['assetId', 'version'])
@Index(['status', 'createdAt'])
export class AssetVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 20 })
  @Index()
  version: string; // 语义化版本号，如 1.0.0

  @Column({ type: 'text', nullable: true })
  changelog: string; // 版本更新日志

  @Column({
    type: 'enum',
    enum: VersionStatus,
    default: VersionStatus.DRAFT,
  })
  @Index()
  status: VersionStatus;

  // 文件信息
  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  @Column({ name: 'file_hash', length: 64 })
  fileHash: string; // SHA-256 哈希值

  @Column({ name: 'file_format', length: 50 })
  fileFormat: string;

  // 缩略图和预览
  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath: string;

  @Column({ name: 'preview_path', nullable: true })
  previewPath: string;

  // 元数据
  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  // 版本特定的统计
  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'is_current', default: false })
  isCurrent: boolean; // 是否为当前版本

  // 关联关系
  @ManyToOne(() => Asset, asset => asset.versions, { onDelete: 'CASCADE' })
  asset: Asset;

  @Column({ name: 'asset_id' })
  @Index()
  assetId: string;

  @ManyToOne(() => User)
  creator: User;

  @Column({ name: 'creator_id' })
  creatorId: string;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'published_at', nullable: true })
  publishedAt: Date;

  // 计算属性
  get isPublished(): boolean {
    return this.status === VersionStatus.PUBLISHED;
  }

  get fileSizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = Number(this.fileSize);
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  get versionParts(): { major: number; minor: number; patch: number } {
    const parts = this.version.split('.').map(Number);
    return {
      major: parts[0] || 0,
      minor: parts[1] || 0,
      patch: parts[2] || 0,
    };
  }

  get isNewerThan(): (otherVersion: string) => boolean {
    return (otherVersion: string) => {
      const other = otherVersion.split('.').map(Number);
      const current = this.version.split('.').map(Number);
      
      for (let i = 0; i < 3; i++) {
        const currentPart = current[i] || 0;
        const otherPart = other[i] || 0;
        
        if (currentPart > otherPart) return true;
        if (currentPart < otherPart) return false;
      }
      
      return false;
    };
  }
}
