# 微服务间通信机制

本文档详细介绍了文本语音场景生成系统的微服务间通信架构和实现方案。

## 🏗️ 架构概览

### 核心组件

1. **API网关** (端口: 8000)
   - 统一入口和路由
   - 负载均衡和服务发现
   - 认证授权和限流
   - 请求代理和响应聚合

2. **服务注册中心** (端口: 8010)
   - 服务注册和发现
   - 健康检查和监控
   - 服务元数据管理

3. **微服务集群**
   - 用户服务 (8001)
   - AI模型服务 (8002)
   - 资源库服务 (8003)
   - 场景模板服务 (8004)
   - 场景生成服务 (8005)
   - 项目服务 (8006)
   - 渲染服务 (8007)
   - 知识库服务 (8008)
   - RAG引擎 (8009)

## 🔄 通信模式

### 1. 同步通信 (HTTP/REST)

#### API网关路由配置
```typescript
const routes = [
  { path: '/api/users', serviceName: 'user-service' },
  { path: '/api/projects', serviceName: 'project-service' },
  { path: '/api/assets', serviceName: 'asset-library-service' },
  { path: '/api/templates', serviceName: 'scene-template-service' },
  { path: '/api/ai-models', serviceName: 'ai-model-service' },
  { path: '/api/generation', serviceName: 'scene-generation-service' },
  { path: '/api/render', serviceName: 'render-service' },
  { path: '/api/knowledge', serviceName: 'knowledge-service' },
  { path: '/api/rag', serviceName: 'rag-engine' },
];
```

#### 微服务客户端使用示例
```typescript
// 在场景生成服务中调用AI模型服务
@Injectable()
export class SceneGenerationService {
  constructor(
    private readonly microserviceClient: MicroserviceClientService
  ) {}

  async generateScene(textInput: string) {
    // 调用AI模型服务进行文本分析
    const analysisResult = await this.microserviceClient.post(
      'ai-model-service',
      '/models/text-analysis/predict',
      { text: textInput }
    );

    // 调用资源库服务获取相关资源
    const assets = await this.microserviceClient.get(
      'asset-library-service',
      '/assets/search',
      { params: { keywords: analysisResult.keywords } }
    );

    return { analysisResult, assets };
  }
}
```

### 2. 异步通信 (事件驱动)

#### 事件发布示例
```typescript
@Injectable()
export class UserService {
  constructor(private readonly eventBus: EventBusService) {}

  async createUser(userData: CreateUserDto) {
    const user = await this.userRepository.save(userData);

    // 发布用户创建事件
    await this.eventBus.publish({
      type: 'user.created',
      aggregateId: user.id,
      aggregateType: 'User',
      version: 1,
      data: {
        userId: user.id,
        email: user.email,
        username: user.username,
      },
    });

    return user;
  }
}
```

#### 事件订阅示例
```typescript
@Injectable()
export class ProjectService implements OnModuleInit {
  constructor(private readonly eventBus: EventBusService) {}

  onModuleInit() {
    // 订阅用户创建事件
    this.eventBus.subscribe('user.created', {
      handle: async (event) => {
        // 为新用户创建默认项目
        await this.createDefaultProject(event.data.userId);
      },
    });
  }
}
```

### 3. 实时通信 (WebSocket)

#### WebSocket网关
```typescript
@WebSocketGateway({
  cors: { origin: '*' },
  namespace: '/generation',
})
export class GenerationGateway {
  @SubscribeMessage('start_generation')
  async handleGeneration(
    @MessageBody() data: any,
    @ConnectedSocket() client: Socket,
  ) {
    // 启动场景生成任务
    const task = await this.generationService.createTask(data);
    
    // 实时推送进度更新
    this.sendProgressUpdate(client, task.id);
  }

  private sendProgressUpdate(client: Socket, taskId: string) {
    // 监听任务进度事件
    this.eventBus.subscribe(`generation.progress.${taskId}`, {
      handle: async (event) => {
        client.emit('generation_progress', event.data);
      },
    });
  }
}
```

## 🛡️ 可靠性保障

### 1. 熔断器模式

```typescript
@Injectable()
export class AssetService {
  constructor(
    private readonly circuitBreaker: CircuitBreakerService
  ) {}

  @CircuitBreaker('ai-model-service', {
    failureThreshold: 5,
    resetTimeout: 60000,
  })
  async analyzeAsset(assetId: string) {
    return this.microserviceClient.post(
      'ai-model-service',
      '/models/image-analysis/predict',
      { assetId }
    );
  }
}
```

### 2. 重试机制

```typescript
const retryConfig = {
  retries: 3,
  retryDelay: 1000,
  retryCondition: (error) => {
    return error.response?.status >= 500;
  },
};

await this.microserviceClient.post(
  'render-service',
  '/render/scene',
  sceneData,
  { retryConfig }
);
```

### 3. 超时控制

```typescript
await this.microserviceClient.post(
  'scene-generation-service',
  '/generation/create',
  taskData,
  { timeout: 300000 } // 5分钟超时
);
```

## 🔐 安全机制

### 1. 服务间认证

```typescript
// JWT令牌传递
const headers = {
  'Authorization': `Bearer ${serviceToken}`,
  'X-Service-Name': 'api-gateway',
  'X-Request-ID': requestId,
};

await this.microserviceClient.post(
  'user-service',
  '/users/validate',
  { userId },
  { headers }
);
```

### 2. API限流

```typescript
const rateLimitConfig = {
  windowMs: 60000, // 1分钟
  max: 100, // 最多100个请求
  skipSuccessfulRequests: false,
};
```

## 📊 监控和追踪

### 1. 分布式追踪

```typescript
// 请求链路追踪
const traceHeaders = {
  'X-Trace-ID': generateTraceId(),
  'X-Span-ID': generateSpanId(),
  'X-Parent-Span-ID': parentSpanId,
};
```

### 2. 性能监控

```typescript
// Prometheus指标收集
REQUEST_COUNT.labels({
  method: 'POST',
  service: 'scene-generation-service',
  endpoint: '/generation/create',
  status: '200',
}).inc();

REQUEST_DURATION.labels({
  method: 'POST',
  service: 'scene-generation-service',
  endpoint: '/generation/create',
}).observe(responseTime);
```

### 3. 健康检查

```typescript
@Controller('health')
export class HealthController {
  @Get()
  async checkHealth() {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkExternalServices(),
    ]);

    return {
      status: checks.every(c => c.status === 'fulfilled') ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      checks: checks.map(c => ({
        status: c.status,
        value: c.status === 'fulfilled' ? c.value : c.reason,
      })),
    };
  }
}
```

## 🚀 部署和运维

### 1. 服务启动顺序

```bash
# 1. 基础设施
docker-compose up -d postgres redis minio elasticsearch

# 2. 监控服务
docker-compose up -d prometheus grafana jaeger

# 3. 服务注册中心
docker-compose up -d service-registry

# 4. 核心微服务
docker-compose up -d user-service ai-model-service asset-library-service

# 5. 业务微服务
docker-compose up -d scene-generation-service project-service

# 6. API网关
docker-compose up -d api-gateway
```

### 2. 配置管理

```yaml
# docker-compose.microservices.yml
environment:
  - SERVICE_REGISTRY_URL=http://service-registry:8010
  - REDIS_URL=redis://redis:6379
  - DATABASE_URL=********************************************/microservices
  - AI_MODEL_SERVICE_URL=http://ai-model-service:8002
  - ASSET_LIBRARY_SERVICE_URL=http://asset-library-service:8003
```

### 3. 日志聚合

```typescript
// 结构化日志
this.logger.log('场景生成开始', {
  taskId: task.id,
  userId: task.userId,
  inputType: task.type,
  timestamp: new Date().toISOString(),
  service: 'scene-generation-service',
});
```

## 🔧 故障排除

### 常见问题

1. **服务发现失败**
   ```bash
   # 检查服务注册中心状态
   curl http://localhost:8010/health
   
   # 查看注册的服务
   curl http://localhost:8010/registry/services
   ```

2. **负载均衡问题**
   ```bash
   # 检查负载均衡统计
   curl http://localhost:8000/gateway/load-balancer/stats
   ```

3. **熔断器触发**
   ```bash
   # 查看熔断器状态
   curl http://localhost:8000/gateway/health
   ```

### 性能调优

1. **连接池配置**
   ```typescript
   const httpClient = axios.create({
     timeout: 30000,
     maxRedirects: 5,
     maxContentLength: 100 * 1024 * 1024, // 100MB
   });
   ```

2. **缓存策略**
   ```typescript
   // 缓存频繁访问的数据
   await this.cacheService.set(
     `user:${userId}`,
     userData,
     3600 // 1小时缓存
   );
   ```

3. **批量处理**
   ```typescript
   // 批量请求优化
   const batchRequests = assets.map(asset => ({
     serviceName: 'ai-model-service',
     method: 'POST',
     path: '/models/analyze',
     data: { assetId: asset.id },
   }));

   const results = await this.microserviceClient.batchRequest(batchRequests);
   ```

## 📈 扩展性考虑

### 1. 水平扩展

```yaml
# 扩展场景生成服务
docker-compose up -d --scale scene-generation-service=3
```

### 2. 数据分片

```typescript
// 基于用户ID的数据分片
const shardKey = userId % 4;
const databaseUrl = `postgresql://postgres:password@postgres-shard-${shardKey}:5432/microservices`;
```

### 3. 缓存分层

```typescript
// L1: 本地缓存
// L2: Redis缓存
// L3: 数据库
const getCachedData = async (key: string) => {
  // 先查本地缓存
  let data = this.localCache.get(key);
  if (data) return data;

  // 再查Redis
  data = await this.redisCache.get(key);
  if (data) {
    this.localCache.set(key, data, 300); // 5分钟本地缓存
    return data;
  }

  // 最后查数据库
  data = await this.database.find(key);
  if (data) {
    await this.redisCache.set(key, data, 3600); // 1小时Redis缓存
    this.localCache.set(key, data, 300);
  }

  return data;
};
```

## 🎯 最佳实践

1. **服务边界清晰**：每个微服务负责单一业务领域
2. **数据一致性**：使用事件溯源和SAGA模式
3. **故障隔离**：熔断器和舱壁模式
4. **可观测性**：全链路追踪和监控
5. **自动化运维**：CI/CD和自动扩缩容

通过这套完整的微服务间通信机制，文本语音场景生成系统能够实现高可用、高性能、可扩展的分布式架构。
