"""
对象存储服务模块
"""

import asyncio
import hashlib
import os
from pathlib import Path
from typing import Optional, Dict, Any, BinaryIO, Union
from urllib.parse import urlparse

from minio import Minio
from minio.error import S3Error

from ..config import get_settings
from .logger import logger

# 全局存储客户端
storage_client: Optional[Minio] = None


async def init_storage() -> None:
    """初始化存储服务"""
    global storage_client
    
    settings = get_settings()
    
    try:
        # 创建MinIO客户端
        storage_client = Minio(
            settings.minio_endpoint,
            access_key=settings.minio_access_key,
            secret_key=settings.minio_secret_key,
            secure=settings.minio_secure,
        )
        
        # 确保存储桶存在
        await _ensure_buckets_exist()
        
        logger.info("存储服务初始化成功")
        
    except Exception as e:
        logger.error(f"存储服务初始化失败: {e}")
        raise


async def _ensure_buckets_exist() -> None:
    """确保存储桶存在"""
    settings = get_settings()
    buckets = [
        settings.minio_bucket_models,
        settings.minio_bucket_cache,
    ]
    
    for bucket in buckets:
        try:
            if not storage_client.bucket_exists(bucket):
                storage_client.make_bucket(bucket)
                logger.info(f"创建存储桶: {bucket}")
        except S3Error as e:
            logger.error(f"创建存储桶失败 {bucket}: {e}")
            raise


async def get_storage_client() -> Minio:
    """获取存储客户端"""
    if not storage_client:
        raise RuntimeError("存储服务未初始化")
    return storage_client


class StorageManager:
    """存储管理器"""
    
    def __init__(self):
        self.client: Optional[Minio] = None
        self.settings = get_settings()
    
    async def initialize(self) -> None:
        """初始化存储管理器"""
        await init_storage()
        self.client = storage_client
    
    async def cleanup(self) -> None:
        """清理存储连接"""
        # MinIO客户端不需要显式关闭
        pass
    
    async def health_check(self) -> bool:
        """存储健康检查"""
        try:
            if not self.client:
                return False
            
            # 尝试列出存储桶
            buckets = self.client.list_buckets()
            return len(buckets) >= 0
            
        except Exception as e:
            logger.error(f"存储健康检查失败: {e}")
            return False
    
    async def upload_file(
        self,
        bucket: str,
        object_name: str,
        file_path: Union[str, Path],
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """上传文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 计算文件哈希
            file_hash = await self._calculate_file_hash(file_path)
            file_size = file_path.stat().st_size
            
            # 设置元数据
            if metadata is None:
                metadata = {}
            metadata.update({
                'file-hash': file_hash,
                'file-size': str(file_size),
                'original-name': file_path.name,
            })
            
            # 上传文件
            result = self.client.fput_object(
                bucket,
                object_name,
                str(file_path),
                content_type=content_type,
                metadata=metadata
            )
            
            logger.info(f"文件上传成功: {bucket}/{object_name}")
            
            return {
                'bucket': bucket,
                'object_name': object_name,
                'etag': result.etag,
                'file_hash': file_hash,
                'file_size': file_size,
                'content_type': content_type,
            }
            
        except Exception as e:
            logger.error(f"文件上传失败 {bucket}/{object_name}: {e}")
            raise
    
    async def upload_data(
        self,
        bucket: str,
        object_name: str,
        data: bytes,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """上传数据"""
        try:
            # 计算数据哈希
            data_hash = hashlib.sha256(data).hexdigest()
            data_size = len(data)
            
            # 设置元数据
            if metadata is None:
                metadata = {}
            metadata.update({
                'data-hash': data_hash,
                'data-size': str(data_size),
            })
            
            # 上传数据
            from io import BytesIO
            result = self.client.put_object(
                bucket,
                object_name,
                BytesIO(data),
                length=data_size,
                content_type=content_type,
                metadata=metadata
            )
            
            logger.info(f"数据上传成功: {bucket}/{object_name}")
            
            return {
                'bucket': bucket,
                'object_name': object_name,
                'etag': result.etag,
                'data_hash': data_hash,
                'data_size': data_size,
                'content_type': content_type,
            }
            
        except Exception as e:
            logger.error(f"数据上传失败 {bucket}/{object_name}: {e}")
            raise
    
    async def download_file(
        self,
        bucket: str,
        object_name: str,
        file_path: Union[str, Path]
    ) -> Dict[str, Any]:
        """下载文件"""
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            self.client.fget_object(bucket, object_name, str(file_path))
            
            # 获取对象信息
            stat = self.client.stat_object(bucket, object_name)
            
            logger.info(f"文件下载成功: {bucket}/{object_name} -> {file_path}")
            
            return {
                'bucket': bucket,
                'object_name': object_name,
                'file_path': str(file_path),
                'file_size': stat.size,
                'etag': stat.etag,
                'last_modified': stat.last_modified,
                'metadata': stat.metadata,
            }
            
        except Exception as e:
            logger.error(f"文件下载失败 {bucket}/{object_name}: {e}")
            raise
    
    async def download_data(
        self,
        bucket: str,
        object_name: str
    ) -> bytes:
        """下载数据"""
        try:
            response = self.client.get_object(bucket, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            
            logger.info(f"数据下载成功: {bucket}/{object_name}")
            return data
            
        except Exception as e:
            logger.error(f"数据下载失败 {bucket}/{object_name}: {e}")
            raise
    
    async def delete_object(self, bucket: str, object_name: str) -> bool:
        """删除对象"""
        try:
            self.client.remove_object(bucket, object_name)
            logger.info(f"对象删除成功: {bucket}/{object_name}")
            return True
            
        except Exception as e:
            logger.error(f"对象删除失败 {bucket}/{object_name}: {e}")
            return False
    
    async def object_exists(self, bucket: str, object_name: str) -> bool:
        """检查对象是否存在"""
        try:
            self.client.stat_object(bucket, object_name)
            return True
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return False
            raise
    
    async def get_object_info(self, bucket: str, object_name: str) -> Optional[Dict[str, Any]]:
        """获取对象信息"""
        try:
            stat = self.client.stat_object(bucket, object_name)
            return {
                'bucket': bucket,
                'object_name': object_name,
                'size': stat.size,
                'etag': stat.etag,
                'last_modified': stat.last_modified,
                'content_type': stat.content_type,
                'metadata': stat.metadata,
            }
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return None
            raise
    
    async def list_objects(
        self,
        bucket: str,
        prefix: str = "",
        recursive: bool = False
    ) -> List[Dict[str, Any]]:
        """列出对象"""
        try:
            objects = []
            for obj in self.client.list_objects(bucket, prefix=prefix, recursive=recursive):
                objects.append({
                    'object_name': obj.object_name,
                    'size': obj.size,
                    'etag': obj.etag,
                    'last_modified': obj.last_modified,
                    'is_dir': obj.is_dir,
                })
            return objects
            
        except Exception as e:
            logger.error(f"列出对象失败 {bucket}: {e}")
            raise
    
    async def generate_presigned_url(
        self,
        bucket: str,
        object_name: str,
        expires: int = 3600,
        method: str = "GET"
    ) -> str:
        """生成预签名URL"""
        try:
            from datetime import timedelta
            url = self.client.presigned_url(
                method,
                bucket,
                object_name,
                expires=timedelta(seconds=expires)
            )
            return url
            
        except Exception as e:
            logger.error(f"生成预签名URL失败 {bucket}/{object_name}: {e}")
            raise
    
    async def copy_object(
        self,
        source_bucket: str,
        source_object: str,
        dest_bucket: str,
        dest_object: str
    ) -> bool:
        """复制对象"""
        try:
            from minio.commonconfig import CopySource
            copy_source = CopySource(source_bucket, source_object)
            self.client.copy_object(dest_bucket, dest_object, copy_source)
            
            logger.info(f"对象复制成功: {source_bucket}/{source_object} -> {dest_bucket}/{dest_object}")
            return True
            
        except Exception as e:
            logger.error(f"对象复制失败: {e}")
            return False
    
    async def get_bucket_info(self, bucket: str) -> Dict[str, Any]:
        """获取存储桶信息"""
        try:
            # 获取存储桶统计信息
            objects = list(self.client.list_objects(bucket, recursive=True))
            total_size = sum(obj.size for obj in objects)
            object_count = len(objects)
            
            return {
                'bucket': bucket,
                'object_count': object_count,
                'total_size': total_size,
                'total_size_human': self._format_size(total_size),
            }
            
        except Exception as e:
            logger.error(f"获取存储桶信息失败 {bucket}: {e}")
            return {}
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.2f}{size_names[i]}"


# 全局存储管理器实例
storage_manager = StorageManager()
