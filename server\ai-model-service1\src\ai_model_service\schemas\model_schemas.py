"""
模型相关的数据模式
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from uuid import UUID

from pydantic import BaseModel, Field, validator

from ..models.ai_model import ModelType, ModelFormat, ModelStatus


class ModelBase(BaseModel):
    """模型基础模式"""
    name: str = Field(..., min_length=1, max_length=255, description="模型名称")
    display_name: Optional[str] = Field(None, max_length=255, description="显示名称")
    description: Optional[str] = Field(None, description="模型描述")
    version: str = Field(default="1.0.0", description="模型版本")
    model_type: ModelType = Field(..., description="模型类型")
    model_format: ModelFormat = Field(..., description="模型格式")


class ModelCreate(ModelBase):
    """创建模型的数据模式"""
    input_schema: Optional[Dict[str, Any]] = Field(None, description="输入数据结构")
    output_schema: Optional[Dict[str, Any]] = Field(None, description="输出数据结构")
    parameters: Optional[Dict[str, Any]] = Field(None, description="模型参数")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    
    # 硬件要求
    requires_gpu: bool = Field(default=False, description="是否需要GPU")
    min_gpu_memory_mb: Optional[int] = Field(None, ge=0, description="最小GPU内存(MB)")
    min_ram_mb: Optional[int] = Field(None, ge=0, description="最小内存(MB)")
    supported_devices: Optional[List[str]] = Field(None, description="支持的设备")
    
    # 权限
    is_public: bool = Field(default=False, description="是否公开")
    
    @validator('supported_devices')
    def validate_supported_devices(cls, v):
        if v is not None:
            valid_devices = ['cpu', 'cuda', 'mps']
            for device in v:
                if not any(device.startswith(valid) for valid in valid_devices):
                    raise ValueError(f"不支持的设备类型: {device}")
        return v


class ModelUpdate(BaseModel):
    """更新模型的数据模式"""
    display_name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    requires_gpu: Optional[bool] = None
    min_gpu_memory_mb: Optional[int] = Field(None, ge=0)
    min_ram_mb: Optional[int] = Field(None, ge=0)
    supported_devices: Optional[List[str]] = None
    is_public: Optional[bool] = None


class ModelResponse(ModelBase):
    """模型响应数据模式"""
    id: UUID
    status: ModelStatus
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    file_hash: Optional[str] = None
    config_path: Optional[str] = None
    
    # 性能信息
    model_size_mb: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    gpu_memory_mb: Optional[float] = None
    inference_time_ms: Optional[float] = None
    
    # 硬件要求
    requires_gpu: bool
    min_gpu_memory_mb: Optional[int] = None
    min_ram_mb: Optional[int] = None
    supported_devices: Optional[List[str]] = None
    
    # 使用统计
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time_ms: Optional[float] = None
    last_used_at: Optional[datetime] = None
    
    # 版本控制
    is_latest: bool = True
    parent_model_id: Optional[UUID] = None
    
    # 权限和可见性
    is_public: bool
    is_active: bool
    created_by: Optional[str] = None
    
    # 时间戳
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    
    # 输入输出模式
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests


class ModelListResponse(BaseModel):
    """模型列表响应"""
    models: List[ModelResponse]
    total: int
    skip: int
    limit: int


class ModelUploadResponse(BaseModel):
    """模型上传响应"""
    model_id: str
    file_type: str
    object_name: str
    file_size: int
    file_hash: str
    message: str


class ModelStatusUpdate(BaseModel):
    """模型状态更新"""
    status: ModelStatus
    error_message: Optional[str] = None


class ModelLoadRequest(BaseModel):
    """模型加载请求"""
    force_reload: bool = Field(default=False, description="是否强制重新加载")


class ModelLoadResponse(BaseModel):
    """模型加载响应"""
    model_id: str
    success: bool
    message: str
    device: Optional[str] = None
    memory_usage_mb: Optional[float] = None


class ModelUnloadResponse(BaseModel):
    """模型卸载响应"""
    model_id: str
    success: bool
    message: str


class ModelInfoResponse(BaseModel):
    """模型信息响应"""
    model_id: str
    name: str
    version: str
    model_type: ModelType
    status: ModelStatus
    is_loaded: bool
    device: Optional[str] = None
    memory_usage_mb: Optional[float] = None
    uptime_seconds: Optional[float] = None
    idle_time_seconds: Optional[float] = None
    request_count: Optional[int] = None


class LoadedModelResponse(BaseModel):
    """已加载模型响应"""
    model_id: str
    device: str
    memory_usage_mb: float
    uptime_seconds: float
    idle_time_seconds: float
    request_count: int


class SystemStatusResponse(BaseModel):
    """系统状态响应"""
    loaded_models_count: int
    queue_size: int
    max_queue_size: int
    worker_count: int
    device_info: Dict[str, Any]
    memory_info: Dict[str, Any]
