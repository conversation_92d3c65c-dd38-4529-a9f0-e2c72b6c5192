import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { GenerationTask, TaskStatus, TaskType } from '../tasks/entities/generation-task.entity';
import { Scene, SceneStatus } from '../scenes/entities/scene.entity';
import { LoggerService } from '../../common/services/logger.service';
import { CacheService } from '../../common/services/cache.service';
import { StorageService } from '../../common/services/storage.service';
import { HttpClientService } from '../../common/services/http-client.service';
import { WebsocketGateway } from '../websocket/websocket.gateway';
import { CreateGenerationTaskDto } from './dto/create-generation-task.dto';
import { TextAnalysisService } from './services/text-analysis.service';
import { VoiceProcessingService } from './services/voice-processing.service';
import { LayoutGenerationService } from './services/layout-generation.service';
import { AssetSelectionService } from './services/asset-selection.service';
import { SceneBuildingService } from './services/scene-building.service';
import { SceneOptimizationService } from './services/scene-optimization.service';

export interface GenerationProgress {
  taskId: string;
  status: TaskStatus;
  progress: number;
  currentStep: string;
  estimatedTimeRemaining?: number;
  message?: string;
}

@Injectable()
export class GenerationService {
  constructor(
    @InjectRepository(GenerationTask)
    private readonly taskRepository: Repository<GenerationTask>,
    @InjectRepository(Scene)
    private readonly sceneRepository: Repository<Scene>,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly cacheService: CacheService,
    private readonly storageService: StorageService,
    private readonly httpClientService: HttpClientService,
    private readonly websocketGateway: WebsocketGateway,
    private readonly textAnalysisService: TextAnalysisService,
    private readonly voiceProcessingService: VoiceProcessingService,
    private readonly layoutGenerationService: LayoutGenerationService,
    private readonly assetSelectionService: AssetSelectionService,
    private readonly sceneBuildingService: SceneBuildingService,
    private readonly sceneOptimizationService: SceneOptimizationService,
  ) {}

  /**
   * 创建生成任务
   */
  async createGenerationTask(
    createTaskDto: CreateGenerationTaskDto,
    userId: string,
  ): Promise<GenerationTask> {
    try {
      // 验证输入
      this.validateInput(createTaskDto);

      // 创建任务
      const task = this.taskRepository.create({
        ...createTaskDto,
        userId,
        status: TaskStatus.PENDING,
        progress: 0,
        totalSteps: this.calculateTotalSteps(createTaskDto.type),
      });

      const savedTask = await this.taskRepository.save(task);

      // 异步开始处理
      this.processGenerationTask(savedTask.id).catch((error) => {
        this.logger.error(`任务处理失败: ${savedTask.id}`, error);
      });

      this.logger.log(`生成任务创建成功: ${savedTask.id}`);
      return savedTask;

    } catch (error) {
      this.logger.error('创建生成任务失败', error);
      throw error;
    }
  }

  /**
   * 处理生成任务
   */
  async processGenerationTask(taskId: string): Promise<void> {
    const task = await this.taskRepository.findOne({
      where: { id: taskId },
      relations: ['user'],
    });

    if (!task) {
      throw new NotFoundException('任务不存在');
    }

    try {
      // 标记任务开始
      task.markAsStarted();
      await this.taskRepository.save(task);
      await this.notifyProgress(task, '开始处理任务');

      // 步骤1: 输入分析
      await this.analyzeInput(task);

      // 步骤2: 生成布局
      await this.generateLayout(task);

      // 步骤3: 选择资源
      await this.selectAssets(task);

      // 步骤4: 构建场景
      await this.buildScene(task);

      // 步骤5: 优化场景
      await this.optimizeScene(task);

      // 完成任务
      await this.completeTask(task);

    } catch (error) {
      await this.failTask(task, error);
    }
  }

  /**
   * 分析输入
   */
  private async analyzeInput(task: GenerationTask): Promise<void> {
    task.status = TaskStatus.ANALYZING_INPUT;
    task.currentStep = '分析输入内容';
    await this.taskRepository.save(task);
    await this.notifyProgress(task, '正在分析输入内容...');

    let analysisResult: any = {};

    try {
      switch (task.type) {
        case TaskType.TEXT_TO_SCENE:
          if (!task.textInput) {
            throw new Error('文本输入为空');
          }
          analysisResult = await this.textAnalysisService.analyzeText(task.textInput);
          break;

        case TaskType.VOICE_TO_SCENE:
          if (!task.voiceFileUrl) {
            throw new Error('语音文件为空');
          }
          // 先转换语音为文本
          const transcription = await this.voiceProcessingService.transcribeAudio(task.voiceFileUrl);
          // 然后分析文本
          analysisResult = await this.textAnalysisService.analyzeText(transcription.text);
          analysisResult.transcription = transcription;
          break;

        case TaskType.IMAGE_TO_SCENE:
          if (!task.imageFileUrl) {
            throw new Error('图片文件为空');
          }
          analysisResult = await this.analyzeImage(task.imageFileUrl);
          break;

        case TaskType.TEMPLATE_BASED:
          if (!task.templateId) {
            throw new Error('模板ID为空');
          }
          analysisResult = await this.analyzeTemplate(task.templateId);
          break;

        default:
          throw new Error(`不支持的任务类型: ${task.type}`);
      }

      // 保存分析结果
      task.analysisResult = analysisResult;
      task.updateProgress(20, '输入分析完成');
      await this.taskRepository.save(task);
      await this.notifyProgress(task, '输入分析完成');

    } catch (error) {
      this.logger.error(`输入分析失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 生成布局
   */
  private async generateLayout(task: GenerationTask): Promise<void> {
    task.status = TaskStatus.GENERATING_LAYOUT;
    task.currentStep = '生成场景布局';
    await this.taskRepository.save(task);
    await this.notifyProgress(task, '正在生成场景布局...');

    try {
      const layoutData = await this.layoutGenerationService.generateLayout(
        task.analysisResult,
        task.generationConfig,
        task.stylePreferences,
        task.sceneConstraints,
      );

      task.layoutData = layoutData;
      task.updateProgress(40, '场景布局生成完成');
      await this.taskRepository.save(task);
      await this.notifyProgress(task, '场景布局生成完成');

    } catch (error) {
      this.logger.error(`布局生成失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 选择资源
   */
  private async selectAssets(task: GenerationTask): Promise<void> {
    task.status = TaskStatus.SELECTING_ASSETS;
    task.currentStep = '选择场景资源';
    await this.taskRepository.save(task);
    await this.notifyProgress(task, '正在选择场景资源...');

    try {
      const selectedAssets = await this.assetSelectionService.selectAssets(
        task.layoutData,
        task.analysisResult,
        task.stylePreferences,
      );

      task.selectedAssets = selectedAssets;
      task.updateProgress(60, '资源选择完成');
      await this.taskRepository.save(task);
      await this.notifyProgress(task, '资源选择完成');

    } catch (error) {
      this.logger.error(`资源选择失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 构建场景
   */
  private async buildScene(task: GenerationTask): Promise<void> {
    task.status = TaskStatus.BUILDING_SCENE;
    task.currentStep = '构建3D场景';
    await this.taskRepository.save(task);
    await this.notifyProgress(task, '正在构建3D场景...');

    try {
      const sceneData = await this.sceneBuildingService.buildScene(
        task.layoutData,
        task.selectedAssets,
        task.generationConfig,
      );

      task.sceneData = sceneData;
      task.updateProgress(80, '场景构建完成');
      await this.taskRepository.save(task);
      await this.notifyProgress(task, '场景构建完成');

    } catch (error) {
      this.logger.error(`场景构建失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 优化场景
   */
  private async optimizeScene(task: GenerationTask): Promise<void> {
    task.status = TaskStatus.OPTIMIZING;
    task.currentStep = '优化场景性能';
    await this.taskRepository.save(task);
    await this.notifyProgress(task, '正在优化场景性能...');

    try {
      const optimizedData = await this.sceneOptimizationService.optimizeScene(
        task.sceneData,
        task.generationConfig,
      );

      // 生成场景文件
      const sceneFileUrl = await this.generateSceneFile(optimizedData, task);

      // 生成预览图
      const previewImageUrl = await this.generatePreviewImage(optimizedData, task);

      // 更新任务
      task.sceneData = optimizedData;
      task.outputFileUrl = sceneFileUrl;
      task.previewImageUrl = previewImageUrl;
      task.updateProgress(95, '场景优化完成');
      await this.taskRepository.save(task);
      await this.notifyProgress(task, '场景优化完成');

    } catch (error) {
      this.logger.error(`场景优化失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 完成任务
   */
  private async completeTask(task: GenerationTask): Promise<void> {
    try {
      // 创建场景记录
      const scene = await this.createSceneFromTask(task);

      // 标记任务完成
      task.markAsCompleted(task.outputFileUrl);
      task.updateProgress(100, '任务完成');
      await this.taskRepository.save(task);

      // 通知完成
      await this.notifyProgress(task, '场景生成完成！', {
        sceneId: scene.id,
        sceneUrl: task.outputFileUrl,
        previewUrl: task.previewImageUrl,
      });

      this.logger.log(`生成任务完成: ${task.id}`);

    } catch (error) {
      this.logger.error(`完成任务失败: ${task.id}`, error);
      throw error;
    }
  }

  /**
   * 任务失败处理
   */
  private async failTask(task: GenerationTask, error: any): Promise<void> {
    const errorMessage = error.message || '未知错误';
    const errorCode = error.code || 'GENERATION_ERROR';

    task.markAsFailed(errorMessage, errorCode, {
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });

    await this.taskRepository.save(task);
    await this.notifyProgress(task, `任务失败: ${errorMessage}`);

    this.logger.error(`生成任务失败: ${task.id}`, error);
  }

  /**
   * 通知进度更新
   */
  private async notifyProgress(
    task: GenerationTask,
    message: string,
    additionalData?: any,
  ): Promise<void> {
    const progress: GenerationProgress = {
      taskId: task.id,
      status: task.status,
      progress: task.progress,
      currentStep: task.currentStep,
      estimatedTimeRemaining: task.estimatedTimeRemaining,
      message,
    };

    // 通过WebSocket发送进度更新
    this.websocketGateway.sendToUser(task.userId, 'generation_progress', {
      ...progress,
      ...additionalData,
    });

    // 缓存最新进度
    await this.cacheService.set(
      `generation_progress:${task.id}`,
      progress,
      300, // 5分钟缓存
    );
  }

  /**
   * 验证输入
   */
  private validateInput(createTaskDto: CreateGenerationTaskDto): void {
    switch (createTaskDto.type) {
      case TaskType.TEXT_TO_SCENE:
        if (!createTaskDto.textInput || createTaskDto.textInput.trim().length === 0) {
          throw new BadRequestException('文本输入不能为空');
        }
        if (createTaskDto.textInput.length > 5000) {
          throw new BadRequestException('文本输入过长，最多5000字符');
        }
        break;

      case TaskType.VOICE_TO_SCENE:
        if (!createTaskDto.voiceFileUrl) {
          throw new BadRequestException('语音文件不能为空');
        }
        break;

      case TaskType.IMAGE_TO_SCENE:
        if (!createTaskDto.imageFileUrl) {
          throw new BadRequestException('图片文件不能为空');
        }
        break;

      case TaskType.TEMPLATE_BASED:
        if (!createTaskDto.templateId) {
          throw new BadRequestException('模板ID不能为空');
        }
        break;

      default:
        throw new BadRequestException(`不支持的任务类型: ${createTaskDto.type}`);
    }
  }

  /**
   * 计算总步骤数
   */
  private calculateTotalSteps(type: TaskType): number {
    // 根据任务类型返回不同的步骤数
    switch (type) {
      case TaskType.TEXT_TO_SCENE:
      case TaskType.VOICE_TO_SCENE:
        return 5; // 分析、布局、资源、构建、优化
      case TaskType.IMAGE_TO_SCENE:
        return 6; // 图像分析、分析、布局、资源、构建、优化
      case TaskType.TEMPLATE_BASED:
        return 4; // 模板分析、资源、构建、优化
      default:
        return 5;
    }
  }

  /**
   * 分析图片
   */
  private async analyzeImage(imageUrl: string): Promise<any> {
    // 调用AI模型服务分析图片
    const response = await this.httpClientService.post('/ai-models/image-analysis', {
      imageUrl,
      analysisType: 'scene_understanding',
    });

    return response.data;
  }

  /**
   * 分析模板
   */
  private async analyzeTemplate(templateId: string): Promise<any> {
    // 调用模板服务获取模板信息
    const response = await this.httpClientService.get(`/scene-templates/${templateId}`);
    return response.data;
  }

  /**
   * 生成场景文件
   */
  private async generateSceneFile(sceneData: any, task: GenerationTask): Promise<string> {
    // 将场景数据转换为GLTF格式
    const gltfData = await this.convertToGLTF(sceneData);
    
    // 上传到存储服务
    const fileName = `scenes/${task.id}/scene.gltf`;
    const fileUrl = await this.storageService.uploadBuffer(
      Buffer.from(JSON.stringify(gltfData)),
      fileName,
      'application/json',
    );

    return fileUrl;
  }

  /**
   * 生成预览图
   */
  private async generatePreviewImage(sceneData: any, task: GenerationTask): Promise<string> {
    // 调用渲染服务生成预览图
    const response = await this.httpClientService.post('/rendering/preview', {
      sceneData,
      width: 800,
      height: 600,
      quality: 'high',
    });

    return response.data.imageUrl;
  }

  /**
   * 从任务创建场景记录
   */
  private async createSceneFromTask(task: GenerationTask): Promise<Scene> {
    const scene = this.sceneRepository.create({
      name: task.name,
      description: task.description,
      type: this.inferSceneType(task.analysisResult),
      style: this.inferSceneStyle(task.stylePreferences),
      status: SceneStatus.READY,
      sceneConfig: task.sceneData,
      fileUrl: task.outputFileUrl,
      thumbnailUrl: task.previewImageUrl,
      generationTaskId: task.id,
      userId: task.userId,
      qualityScore: task.qualityScore,
      complexityScore: task.complexityScore,
    });

    return await this.sceneRepository.save(scene);
  }

  /**
   * 推断场景类型
   */
  private inferSceneType(analysisResult: any): any {
    // 根据分析结果推断场景类型
    // 这里是简化实现，实际应该有更复杂的逻辑
    return analysisResult?.sceneType || 'indoor';
  }

  /**
   * 推断场景风格
   */
  private inferSceneStyle(stylePreferences: any): any {
    // 根据风格偏好推断场景风格
    return stylePreferences?.style || 'realistic';
  }

  /**
   * 转换为GLTF格式
   */
  private async convertToGLTF(sceneData: any): Promise<any> {
    // 这里应该实现场景数据到GLTF的转换
    // 简化实现，返回基本的GLTF结构
    return {
      asset: {
        version: '2.0',
        generator: 'Scene Generation Service',
      },
      scene: 0,
      scenes: [
        {
          nodes: sceneData.objects?.map((obj: any, index: number) => index) || [],
        },
      ],
      nodes: sceneData.objects || [],
      meshes: sceneData.meshes || [],
      materials: sceneData.materials || [],
      textures: sceneData.textures || [],
      images: sceneData.images || [],
    };
  }
}
