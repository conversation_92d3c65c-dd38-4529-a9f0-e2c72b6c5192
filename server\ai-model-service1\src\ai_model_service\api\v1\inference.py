"""
推理API路由
"""

import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request, BackgroundTasks
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db_session
from ...core.model_manager import ModelManager
from ...models.ai_model import AIModel, ModelStatus
from ...schemas.inference_schemas import (
    InferenceRequest, InferenceResponse, InferenceListResponse,
    InferenceLogResponse, BatchInferenceRequest, BatchInferenceResponse
)
from ...core.logger import logger, request_logger
from ...config import get_settings

router = APIRouter()
settings = get_settings()


def get_model_manager(request: Request) -> ModelManager:
    """获取模型管理器"""
    return request.app.state.model_manager


@router.post("/predict/{model_id}", response_model=InferenceResponse)
async def predict(
    model_id: str,
    inference_request: InferenceRequest,
    request: Request,
    background_tasks: BackgroundTasks,
    model_manager: ModelManager = Depends(get_model_manager),
    db: AsyncSession = Depends(get_db_session)
):
    """执行模型推理"""
    request_id = str(uuid.uuid4())
    user_id = getattr(request.state, 'user_id', None)
    start_time = datetime.utcnow()
    
    try:
        # 检查模型是否存在且可用
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        if not model.is_ready:
            raise HTTPException(
                status_code=400,
                detail=f"模型状态不可用: {model.status}"
            )
        
        # 验证输入数据
        if model.input_schema:
            # 这里可以添加更复杂的输入验证逻辑
            pass
        
        # 执行推理
        output_data, metadata = await model_manager.predict(
            model_id=model_id,
            input_data=inference_request.input_data,
            request_id=request_id,
            user_id=user_id
        )
        
        # 记录请求日志
        duration = (datetime.utcnow() - start_time).total_seconds()
        background_tasks.add_task(
            request_logger.log_model_inference,
            model_id=model_id,
            model_type=model.model_type.value,
            duration=duration,
            input_size=len(str(inference_request.input_data)),
            output_size=len(str(output_data)),
            request_id=request_id,
            success=True
        )
        
        return InferenceResponse(
            request_id=request_id,
            model_id=model_id,
            output_data=output_data,
            metadata=metadata,
            success=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # 记录错误日志
        duration = (datetime.utcnow() - start_time).total_seconds()
        background_tasks.add_task(
            request_logger.log_model_inference,
            model_id=model_id,
            model_type="unknown",
            duration=duration,
            input_size=len(str(inference_request.input_data)),
            output_size=0,
            request_id=request_id,
            success=False,
            error=str(e)
        )
        
        logger.error(f"推理失败 {model_id}: {e}")
        raise HTTPException(status_code=500, detail=f"推理失败: {str(e)}")


@router.post("/batch/{model_id}", response_model=BatchInferenceResponse)
async def batch_predict(
    model_id: str,
    batch_request: BatchInferenceRequest,
    request: Request,
    background_tasks: BackgroundTasks,
    model_manager: ModelManager = Depends(get_model_manager),
    db: AsyncSession = Depends(get_db_session)
):
    """批量推理"""
    batch_id = str(uuid.uuid4())
    user_id = getattr(request.state, 'user_id', None)
    start_time = datetime.utcnow()
    
    try:
        # 检查模型是否存在且可用
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        if not model.is_ready:
            raise HTTPException(
                status_code=400,
                detail=f"模型状态不可用: {model.status}"
            )
        
        # 检查批量大小限制
        if len(batch_request.inputs) > settings.inference_batch_size:
            raise HTTPException(
                status_code=400,
                detail=f"批量大小超过限制: {settings.inference_batch_size}"
            )
        
        # 执行批量推理
        results = []
        for i, input_data in enumerate(batch_request.inputs):
            try:
                request_id = f"{batch_id}_{i}"
                output_data, metadata = await model_manager.predict(
                    model_id=model_id,
                    input_data=input_data,
                    request_id=request_id,
                    user_id=user_id
                )
                
                results.append({
                    "index": i,
                    "success": True,
                    "output_data": output_data,
                    "metadata": metadata
                })
                
            except Exception as e:
                results.append({
                    "index": i,
                    "success": False,
                    "error": str(e)
                })
        
        # 统计结果
        successful_count = sum(1 for r in results if r["success"])
        failed_count = len(results) - successful_count
        
        # 记录批量推理日志
        duration = (datetime.utcnow() - start_time).total_seconds()
        background_tasks.add_task(
            logger.info,
            f"批量推理完成: {batch_id}, 成功: {successful_count}, 失败: {failed_count}"
        )
        
        return BatchInferenceResponse(
            batch_id=batch_id,
            model_id=model_id,
            results=results,
            total_count=len(batch_request.inputs),
            successful_count=successful_count,
            failed_count=failed_count,
            processing_time_ms=(duration * 1000)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量推理失败 {model_id}: {e}")
        raise HTTPException(status_code=500, detail=f"批量推理失败: {str(e)}")


@router.get("/logs", response_model=InferenceListResponse)
async def get_inference_logs(
    model_id: Optional[str] = None,
    user_id: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    db: AsyncSession = Depends(get_db_session)
):
    """获取推理日志"""
    try:
        from ...models.inference_log import InferenceLog
        from sqlalchemy import func
        
        # 构建查询
        query = select(InferenceLog)
        
        if model_id:
            query = query.where(InferenceLog.model_id == model_id)
        
        if user_id:
            query = query.where(InferenceLog.user_id == user_id)
        
        if status:
            query = query.where(InferenceLog.status == status)
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(InferenceLog.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        logs = result.scalars().all()
        
        return InferenceListResponse(
            logs=[InferenceLogResponse.from_orm(log) for log in logs],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取推理日志失败: {e}")
        raise HTTPException(status_code=500, detail="获取推理日志失败")


@router.get("/logs/{request_id}", response_model=InferenceLogResponse)
async def get_inference_log(
    request_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取单个推理日志"""
    try:
        from ...models.inference_log import InferenceLog
        
        result = await db.execute(
            select(InferenceLog).where(InferenceLog.request_id == request_id)
        )
        log = result.scalar_one_or_none()
        
        if not log:
            raise HTTPException(status_code=404, detail="推理日志不存在")
        
        return InferenceLogResponse.from_orm(log)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取推理日志失败: {e}")
        raise HTTPException(status_code=500, detail="获取推理日志失败")


@router.post("/models/{model_id}/load")
async def load_model(
    model_id: str,
    force_reload: bool = False,
    model_manager: ModelManager = Depends(get_model_manager)
):
    """加载模型"""
    try:
        success = await model_manager.load_model(model_id, force_reload)
        
        if success:
            return {"message": "模型加载成功", "model_id": model_id}
        else:
            raise HTTPException(status_code=500, detail="模型加载失败")
            
    except Exception as e:
        logger.error(f"加载模型失败 {model_id}: {e}")
        raise HTTPException(status_code=500, detail=f"加载模型失败: {str(e)}")


@router.post("/models/{model_id}/unload")
async def unload_model(
    model_id: str,
    model_manager: ModelManager = Depends(get_model_manager)
):
    """卸载模型"""
    try:
        success = await model_manager.unload_model(model_id)
        
        if success:
            return {"message": "模型卸载成功", "model_id": model_id}
        else:
            raise HTTPException(status_code=500, detail="模型卸载失败")
            
    except Exception as e:
        logger.error(f"卸载模型失败 {model_id}: {e}")
        raise HTTPException(status_code=500, detail=f"卸载模型失败: {str(e)}")


@router.get("/models/{model_id}/info")
async def get_model_runtime_info(
    model_id: str,
    model_manager: ModelManager = Depends(get_model_manager)
):
    """获取模型运行时信息"""
    try:
        info = await model_manager.get_model_info(model_id)
        
        if info is None:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        return info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型信息失败 {model_id}: {e}")
        raise HTTPException(status_code=500, detail="获取模型信息失败")


@router.get("/models/loaded")
async def list_loaded_models(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """列出已加载的模型"""
    try:
        models = await model_manager.list_loaded_models()
        return {"loaded_models": models}
        
    except Exception as e:
        logger.error(f"获取已加载模型列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取已加载模型列表失败")


@router.get("/system/status")
async def get_system_status(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """获取系统状态"""
    try:
        status = await model_manager.get_system_status()
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")
