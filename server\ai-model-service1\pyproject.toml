[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-model-service"
version = "1.0.0"
description = "AI模型管理微服务，支持模型注册、版本管理、负载均衡、模型热更新和性能监控"
authors = [
    {name = "DL Engine Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
keywords = ["ai", "machine-learning", "model-management", "microservice", "fastapi"]

dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.0",
    "redis>=5.0.0",
    "torch>=2.1.0",
    "transformers>=4.36.0",
    "Pillow>=10.0.0",
    "numpy>=1.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]
gpu = [
    "torch[cuda]>=2.1.0",
    "torchvision[cuda]>=0.16.0",
    "torchaudio[cuda]>=2.1.0",
]
optimization = [
    "onnx>=1.15.0",
    "onnxruntime>=1.16.0",
    "tensorrt>=8.6.0",
]

[project.urls]
Homepage = "https://github.com/dlengine/ai-model-service"
Documentation = "https://docs.dlengine.com/ai-model-service"
Repository = "https://github.com/dlengine/ai-model-service.git"
Issues = "https://github.com/dlengine/ai-model-service/issues"

[project.scripts]
ai-model-service = "ai_model_service.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ai_model_service"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "torch.*",
    "transformers.*",
    "diffusers.*",
    "librosa.*",
    "cv2.*",
    "trimesh.*",
    "open3d.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU",
]
