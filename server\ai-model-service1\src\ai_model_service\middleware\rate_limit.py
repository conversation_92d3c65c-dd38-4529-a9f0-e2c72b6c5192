"""
限流中间件
基于Redis实现的分布式限流
"""

import time
from typing import Callable

from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware

from ..core.redis import get_redis_client
from ..config import get_settings
from ..core.logger import logger


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.enabled = self.settings.rate_limit_enabled
        self.requests_per_minute = self.settings.rate_limit_requests_per_minute
        self.burst_size = self.settings.rate_limit_burst_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not self.enabled:
            return await call_next(request)
        
        # 获取客户端标识
        client_id = self._get_client_id(request)
        
        # 检查限流
        allowed, remaining, reset_time = await self._check_rate_limit(client_id)
        
        if not allowed:
            # 记录限流日志
            logger.warning(f"Rate limit exceeded for client: {client_id}")
            
            # 返回429错误
            raise HTTPException(
                status_code=429,
                detail="请求过于频繁，请稍后重试",
                headers={
                    "X-RateLimit-Limit": str(self.requests_per_minute),
                    "X-RateLimit-Remaining": str(remaining),
                    "X-RateLimit-Reset": str(reset_time),
                    "Retry-After": str(int(reset_time - time.time())),
                }
            )
        
        # 调用下一个中间件或路由处理器
        response = await call_next(request)
        
        # 在响应头中添加限流信息
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用用户ID
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # 使用IP地址
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    async def _check_rate_limit(self, client_id: str) -> tuple[bool, int, int]:
        """检查限流状态"""
        try:
            redis_client = await get_redis_client()
            current_time = int(time.time())
            window_start = current_time - 60  # 1分钟窗口
            
            # 使用滑动窗口算法
            key = f"rate_limit:{client_id}"
            
            # 清理过期的请求记录
            await redis_client.zremrangebyscore(key, 0, window_start)
            
            # 获取当前窗口内的请求数量
            current_requests = await redis_client.zcard(key)
            
            # 检查是否超过限制
            if current_requests >= self.requests_per_minute:
                # 获取最早的请求时间，计算重置时间
                oldest_request = await redis_client.zrange(key, 0, 0, withscores=True)
                if oldest_request:
                    reset_time = int(oldest_request[0][1]) + 60
                else:
                    reset_time = current_time + 60
                
                return False, 0, reset_time
            
            # 记录当前请求
            await redis_client.zadd(key, {str(current_time): current_time})
            await redis_client.expire(key, 60)  # 设置过期时间
            
            # 计算剩余请求数和重置时间
            remaining = self.requests_per_minute - current_requests - 1
            reset_time = current_time + 60
            
            return True, remaining, reset_time
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # 如果Redis出错，允许请求通过
            return True, self.requests_per_minute, int(time.time()) + 60


class TokenBucketRateLimit:
    """令牌桶限流算法"""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = capacity
        self.last_refill = time.time()
    
    async def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        await self._refill()
        
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False
    
    async def _refill(self):
        """补充令牌"""
        now = time.time()
        elapsed = now - self.last_refill
        
        # 计算需要补充的令牌数
        tokens_to_add = elapsed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now


class DistributedTokenBucket:
    """分布式令牌桶"""
    
    def __init__(self, client_id: str, capacity: int, refill_rate: float):
        self.client_id = client_id
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.key = f"token_bucket:{client_id}"
    
    async def consume(self, tokens: int = 1) -> bool:
        """消费令牌"""
        try:
            redis_client = await get_redis_client()
            
            # 使用Lua脚本保证原子性
            lua_script = """
            local key = KEYS[1]
            local capacity = tonumber(ARGV[1])
            local refill_rate = tonumber(ARGV[2])
            local tokens_requested = tonumber(ARGV[3])
            local now = tonumber(ARGV[4])
            
            local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
            local tokens = tonumber(bucket[1]) or capacity
            local last_refill = tonumber(bucket[2]) or now
            
            -- 计算需要补充的令牌
            local elapsed = now - last_refill
            local tokens_to_add = elapsed * refill_rate
            tokens = math.min(capacity, tokens + tokens_to_add)
            
            -- 检查是否有足够的令牌
            if tokens >= tokens_requested then
                tokens = tokens - tokens_requested
                redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
                redis.call('EXPIRE', key, 3600)  -- 1小时过期
                return 1
            else
                redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
                redis.call('EXPIRE', key, 3600)
                return 0
            end
            """
            
            result = await redis_client.eval(
                lua_script,
                1,
                self.key,
                self.capacity,
                self.refill_rate,
                tokens,
                time.time()
            )
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Distributed token bucket failed: {e}")
            # 如果Redis出错，允许请求通过
            return True
