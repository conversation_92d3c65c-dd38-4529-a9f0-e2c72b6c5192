# AI模型微服务 (AI Model Service)

基于FastAPI构建的高性能AI模型管理微服务，支持模型注册、版本管理、负载均衡、模型热更新和性能监控。

## 功能特性

### 核心功能
- ✅ **模型管理**: 完整的AI模型生命周期管理
- ✅ **动态加载**: 模型热加载和卸载，无需重启服务
- ✅ **版本控制**: 模型版本管理和回滚机制
- ✅ **负载均衡**: 智能模型调度和资源分配
- ✅ **推理服务**: 高性能推理API，支持批量处理
- ✅ **性能监控**: 实时性能指标和资源使用监控

### 高级特性
- 🚀 **GPU支持**: 自动GPU检测和内存管理
- 📊 **指标收集**: Prometheus指标和性能分析
- 🔄 **缓存机制**: 智能推理结果缓存
- 🛡️ **限流保护**: 分布式限流和过载保护
- 📈 **自动扩缩**: 基于负载的模型实例管理
- 🔍 **链路追踪**: 完整的请求链路追踪

## 技术栈

- **框架**: FastAPI + Python 3.11
- **AI/ML**: PyTorch + Transformers + Diffusers
- **数据库**: PostgreSQL + SQLAlchemy
- **缓存**: Redis
- **存储**: MinIO (S3兼容)
- **监控**: Prometheus + Grafana
- **容器**: Docker + CUDA支持

## 快速开始

### 环境要求
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 13+
- Redis 6+
- MinIO
- NVIDIA GPU (可选，用于GPU推理)

### 安装依赖
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 启动开发环境
```bash
# 启动基础设施服务
docker-compose up -d postgres redis minio

# 运行数据库迁移
alembic upgrade head

# 启动开发服务器
python -m uvicorn src.ai_model_service.main:create_app --host 0.0.0.0 --port 8002 --reload
```

### 构建生产版本
```bash
# CPU版本
docker build -t ai-model-service:latest --target production .

# GPU版本
docker build -t ai-model-service:gpu --target gpu-production .

# 启动生产服务
docker run -d \
  --name ai-model-service \
  -p 8002:8002 \
  -e DATABASE_URL=********************************/db \
  ai-model-service:latest
```

## API 文档

启动服务后访问 Swagger 文档：
- 开发环境: http://localhost:8002/docs
- 生产环境: https://your-domain/docs

### 主要API端点

#### 模型管理
- `POST /api/v1/models` - 注册新模型
- `GET /api/v1/models` - 获取模型列表
- `GET /api/v1/models/{id}` - 获取模型详情
- `PUT /api/v1/models/{id}` - 更新模型信息
- `DELETE /api/v1/models/{id}` - 删除模型

#### 模型操作
- `POST /api/v1/models/{id}/upload` - 上传模型文件
- `POST /api/v1/inference/models/{id}/load` - 加载模型
- `POST /api/v1/inference/models/{id}/unload` - 卸载模型
- `GET /api/v1/inference/models/loaded` - 查看已加载模型

#### 推理服务
- `POST /api/v1/inference/predict/{id}` - 单次推理
- `POST /api/v1/inference/batch/{id}` - 批量推理
- `GET /api/v1/inference/logs` - 推理日志
- `GET /api/v1/inference/system/status` - 系统状态

#### 监控指标
- `GET /metrics` - Prometheus指标
- `GET /health` - 健康检查
- `GET /health/ready` - 就绪检查
- `GET /health/live` - 存活检查

## 数据库设计

### 核心实体

#### AIModel (AI模型)
```python
{
  id: UUID,
  name: str,
  version: str,
  model_type: ModelType,
  model_format: ModelFormat,
  status: ModelStatus,
  file_path: str,
  file_size: int,
  memory_usage_mb: float,
  requires_gpu: bool,
  input_schema: dict,
  output_schema: dict,
  parameters: dict,
  metadata: dict
}
```

#### InferenceLog (推理日志)
```python
{
  id: UUID,
  request_id: str,
  model_id: UUID,
  status: InferenceStatus,
  input_data: dict,
  output_data: dict,
  processing_time_ms: float,
  memory_usage_mb: float,
  device_used: str,
  created_at: datetime
}
```

#### ModelDeployment (模型部署)
```python
{
  id: UUID,
  model_id: UUID,
  deployment_name: str,
  status: DeploymentStatus,
  device: str,
  batch_size: int,
  max_concurrent_requests: int,
  health_status: str
}
```

## 模型管理

### 支持的模型类型
- **文本生成**: GPT、BERT、T5等
- **图像生成**: Stable Diffusion、DALL-E等
- **图像分类**: ResNet、EfficientNet等
- **目标检测**: YOLO、R-CNN等
- **语音识别**: Whisper、Wav2Vec等
- **文本转语音**: Tacotron、FastSpeech等
- **场景理解**: 3D场景分析模型
- **自定义模型**: 用户自定义模型

### 支持的模型格式
- **PyTorch**: .pt, .pth文件
- **TensorFlow**: .pb, .h5文件
- **ONNX**: .onnx文件
- **TensorRT**: .trt文件
- **Hugging Face**: 预训练模型
- **自定义格式**: 用户自定义加载器

### 模型生命周期
1. **注册**: 创建模型记录
2. **上传**: 上传模型文件到存储
3. **验证**: 验证模型格式和完整性
4. **部署**: 加载模型到内存
5. **服务**: 提供推理服务
6. **监控**: 性能和资源监控
7. **更新**: 版本更新和热替换
8. **下线**: 卸载和清理

## 推理服务

### 推理流程
1. **请求接收**: 验证输入数据
2. **模型选择**: 负载均衡选择实例
3. **队列管理**: 请求排队和优先级
4. **推理执行**: 模型推理计算
5. **结果返回**: 格式化输出数据
6. **日志记录**: 记录推理日志

### 性能优化
- **批量处理**: 自动批量合并请求
- **模型缓存**: 智能模型加载策略
- **GPU优化**: CUDA内存管理
- **结果缓存**: 相同输入结果缓存
- **异步处理**: 非阻塞推理执行

### 错误处理
- **超时处理**: 推理超时自动取消
- **重试机制**: 失败请求自动重试
- **降级策略**: 模型不可用时的备选方案
- **错误恢复**: 自动错误恢复和重启

## 监控和运维

### 性能指标
- **请求指标**: QPS、响应时间、成功率
- **模型指标**: 推理时间、内存使用、GPU利用率
- **系统指标**: CPU、内存、磁盘、网络
- **业务指标**: 模型使用量、用户活跃度

### 告警规则
- **高延迟**: 响应时间超过阈值
- **高错误率**: 错误率超过5%
- **资源不足**: 内存或GPU使用率超过90%
- **模型异常**: 模型加载失败或推理异常

### 日志管理
- **结构化日志**: JSON格式日志
- **日志级别**: DEBUG、INFO、WARNING、ERROR
- **日志轮转**: 自动日志文件轮转
- **集中收集**: ELK或类似日志系统

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t ai-model-service .

# 运行容器
docker run -d \
  --name ai-model-service \
  -p 8002:8002 \
  -v /path/to/models:/app/models \
  -e DATABASE_URL=postgresql://... \
  ai-model-service
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-model-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-model-service
  template:
    metadata:
      labels:
        app: ai-model-service
    spec:
      containers:
      - name: ai-model-service
        image: ai-model-service:latest
        ports:
        - containerPort: 8002
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "4"
            nvidia.com/gpu: 1
```

### GPU支持
```bash
# 安装NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker

# 运行GPU容器
docker run --gpus all -d \
  --name ai-model-service-gpu \
  -p 8002:8002 \
  ai-model-service:gpu
```

## 开发指南

### 项目结构
```
src/ai_model_service/
├── api/v1/              # API路由
├── core/                # 核心模块
├── models/              # 数据模型
├── schemas/             # 数据模式
├── middleware/          # 中间件
├── config.py            # 配置管理
└── main.py              # 应用入口
```

### 添加新模型类型
1. 在 `ModelType` 枚举中添加新类型
2. 在模型管理器中实现加载逻辑
3. 在推理服务中实现推理逻辑
4. 添加相应的测试用例

### 扩展推理功能
1. 创建新的推理处理器
2. 注册到模型管理器
3. 实现输入输出验证
4. 添加性能监控

## 故障排除

### 常见问题
1. **模型加载失败**: 检查模型文件格式和路径
2. **GPU内存不足**: 调整批量大小或模型并发数
3. **推理超时**: 检查模型复杂度和硬件性能
4. **连接数据库失败**: 检查数据库配置和网络连接

### 性能调优
1. **批量大小**: 根据GPU内存调整批量大小
2. **并发数**: 根据CPU核数调整工作进程数
3. **缓存策略**: 调整缓存TTL和大小
4. **队列大小**: 根据负载调整队列大小

## 许可证

MIT License

## 联系方式

- 项目维护者: DL Engine Team
- 邮箱: <EMAIL>
- 文档: https://docs.dlengine.com
