"""
Redis连接和管理模块
"""

import asyncio
import json
from typing import Any, Optional, Union, List, Dict

import redis.asyncio as redis
from redis.asyncio import ConnectionPool

from ..config import get_settings
from .logger import logger

# 全局Redis客户端
redis_client: Optional[redis.Redis] = None
redis_pool: Optional[ConnectionPool] = None


async def init_redis() -> None:
    """初始化Redis连接"""
    global redis_client, redis_pool
    
    settings = get_settings()
    
    try:
        # 创建连接池
        redis_pool = ConnectionPool.from_url(
            settings.redis_url,
            password=settings.redis_password,
            db=settings.redis_db,
            max_connections=settings.redis_max_connections,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
        )
        
        # 创建Redis客户端
        redis_client = redis.Redis(connection_pool=redis_pool)
        
        # 测试连接
        await redis_client.ping()
        
        logger.info("Redis连接初始化成功")
        
    except Exception as e:
        logger.error(f"Redis连接初始化失败: {e}")
        raise


async def close_redis() -> None:
    """关闭Redis连接"""
    global redis_client, redis_pool
    
    try:
        if redis_client:
            await redis_client.close()
            logger.info("Redis连接已关闭")
        
        if redis_pool:
            await redis_pool.disconnect()
            logger.info("Redis连接池已关闭")
            
    except Exception as e:
        logger.error(f"关闭Redis连接时出错: {e}")


async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    if not redis_client:
        raise RuntimeError("Redis未初始化")
    return redis_client


class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self.client: Optional[redis.Redis] = None
        self.pool: Optional[ConnectionPool] = None
    
    async def initialize(self) -> None:
        """初始化Redis管理器"""
        await init_redis()
        self.client = redis_client
        self.pool = redis_pool
    
    async def cleanup(self) -> None:
        """清理Redis连接"""
        await close_redis()
    
    async def health_check(self) -> bool:
        """Redis健康检查"""
        try:
            if not self.client:
                return False
            
            await self.client.ping()
            return True
            
        except Exception as e:
            logger.error(f"Redis健康检查失败: {e}")
            return False
    
    async def set(
        self,
        key: str,
        value: Any,
        expire: Optional[int] = None,
        serialize: bool = True
    ) -> bool:
        """设置键值"""
        try:
            if serialize and not isinstance(value, (str, bytes)):
                value = json.dumps(value, ensure_ascii=False)
            
            result = await self.client.set(key, value, ex=expire)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis设置失败 {key}: {e}")
            return False
    
    async def get(
        self,
        key: str,
        deserialize: bool = True
    ) -> Optional[Any]:
        """获取键值"""
        try:
            value = await self.client.get(key)
            if value is None:
                return None
            
            if deserialize:
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value.decode('utf-8') if isinstance(value, bytes) else value
            
            return value.decode('utf-8') if isinstance(value, bytes) else value
            
        except Exception as e:
            logger.error(f"Redis获取失败 {key}: {e}")
            return None
    
    async def delete(self, *keys: str) -> int:
        """删除键"""
        try:
            return await self.client.delete(*keys)
        except Exception as e:
            logger.error(f"Redis删除失败 {keys}: {e}")
            return 0
    
    async def exists(self, *keys: str) -> int:
        """检查键是否存在"""
        try:
            return await self.client.exists(*keys)
        except Exception as e:
            logger.error(f"Redis存在性检查失败 {keys}: {e}")
            return 0
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        try:
            return await self.client.expire(key, seconds)
        except Exception as e:
            logger.error(f"Redis设置过期时间失败 {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取剩余过期时间"""
        try:
            return await self.client.ttl(key)
        except Exception as e:
            logger.error(f"Redis获取TTL失败 {key}: {e}")
            return -1
    
    async def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """递增"""
        try:
            return await self.client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis递增失败 {key}: {e}")
            return None
    
    async def decr(self, key: str, amount: int = 1) -> Optional[int]:
        """递减"""
        try:
            return await self.client.decr(key, amount)
        except Exception as e:
            logger.error(f"Redis递减失败 {key}: {e}")
            return None
    
    async def hset(
        self,
        name: str,
        mapping: Dict[str, Any],
        serialize: bool = True
    ) -> int:
        """设置哈希表"""
        try:
            if serialize:
                mapping = {
                    k: json.dumps(v, ensure_ascii=False) if not isinstance(v, (str, bytes)) else v
                    for k, v in mapping.items()
                }
            
            return await self.client.hset(name, mapping=mapping)
            
        except Exception as e:
            logger.error(f"Redis哈希设置失败 {name}: {e}")
            return 0
    
    async def hget(
        self,
        name: str,
        key: str,
        deserialize: bool = True
    ) -> Optional[Any]:
        """获取哈希表字段"""
        try:
            value = await self.client.hget(name, key)
            if value is None:
                return None
            
            if deserialize:
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value.decode('utf-8') if isinstance(value, bytes) else value
            
            return value.decode('utf-8') if isinstance(value, bytes) else value
            
        except Exception as e:
            logger.error(f"Redis哈希获取失败 {name}.{key}: {e}")
            return None
    
    async def hgetall(
        self,
        name: str,
        deserialize: bool = True
    ) -> Dict[str, Any]:
        """获取整个哈希表"""
        try:
            data = await self.client.hgetall(name)
            if not data:
                return {}
            
            result = {}
            for k, v in data.items():
                key = k.decode('utf-8') if isinstance(k, bytes) else k
                
                if deserialize:
                    try:
                        value = json.loads(v)
                    except (json.JSONDecodeError, TypeError):
                        value = v.decode('utf-8') if isinstance(v, bytes) else v
                else:
                    value = v.decode('utf-8') if isinstance(v, bytes) else v
                
                result[key] = value
            
            return result
            
        except Exception as e:
            logger.error(f"Redis哈希获取全部失败 {name}: {e}")
            return {}
    
    async def hdel(self, name: str, *keys: str) -> int:
        """删除哈希表字段"""
        try:
            return await self.client.hdel(name, *keys)
        except Exception as e:
            logger.error(f"Redis哈希删除失败 {name}.{keys}: {e}")
            return 0
    
    async def lpush(self, name: str, *values: Any) -> int:
        """列表左推"""
        try:
            serialized_values = []
            for value in values:
                if not isinstance(value, (str, bytes)):
                    value = json.dumps(value, ensure_ascii=False)
                serialized_values.append(value)
            
            return await self.client.lpush(name, *serialized_values)
            
        except Exception as e:
            logger.error(f"Redis列表推入失败 {name}: {e}")
            return 0
    
    async def rpop(self, name: str, deserialize: bool = True) -> Optional[Any]:
        """列表右弹"""
        try:
            value = await self.client.rpop(name)
            if value is None:
                return None
            
            if deserialize:
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value.decode('utf-8') if isinstance(value, bytes) else value
            
            return value.decode('utf-8') if isinstance(value, bytes) else value
            
        except Exception as e:
            logger.error(f"Redis列表弹出失败 {name}: {e}")
            return None
    
    async def llen(self, name: str) -> int:
        """获取列表长度"""
        try:
            return await self.client.llen(name)
        except Exception as e:
            logger.error(f"Redis列表长度获取失败 {name}: {e}")
            return 0
    
    async def sadd(self, name: str, *values: Any) -> int:
        """集合添加"""
        try:
            serialized_values = []
            for value in values:
                if not isinstance(value, (str, bytes)):
                    value = json.dumps(value, ensure_ascii=False)
                serialized_values.append(value)
            
            return await self.client.sadd(name, *serialized_values)
            
        except Exception as e:
            logger.error(f"Redis集合添加失败 {name}: {e}")
            return 0
    
    async def srem(self, name: str, *values: Any) -> int:
        """集合移除"""
        try:
            serialized_values = []
            for value in values:
                if not isinstance(value, (str, bytes)):
                    value = json.dumps(value, ensure_ascii=False)
                serialized_values.append(value)
            
            return await self.client.srem(name, *serialized_values)
            
        except Exception as e:
            logger.error(f"Redis集合移除失败 {name}: {e}")
            return 0
    
    async def smembers(self, name: str, deserialize: bool = True) -> set:
        """获取集合成员"""
        try:
            members = await self.client.smembers(name)
            if not members:
                return set()
            
            result = set()
            for member in members:
                if deserialize:
                    try:
                        value = json.loads(member)
                    except (json.JSONDecodeError, TypeError):
                        value = member.decode('utf-8') if isinstance(member, bytes) else member
                else:
                    value = member.decode('utf-8') if isinstance(member, bytes) else member
                
                result.add(value)
            
            return result
            
        except Exception as e:
            logger.error(f"Redis集合成员获取失败 {name}: {e}")
            return set()
    
    async def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        try:
            info = await self.client.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
            }
        except Exception as e:
            logger.error(f"获取Redis连接信息失败: {e}")
            return {}


# 全局Redis管理器实例
redis_manager = RedisManager()
