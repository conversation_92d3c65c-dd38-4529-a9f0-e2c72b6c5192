import { <PERSON>, <PERSON>, Req, Re<PERSON>, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from './proxy.service';

@ApiTags('代理服务')
@Controller('api')
export class ProxyController {
  private readonly logger = new Logger(ProxyController.name);

  constructor(private readonly proxyService: ProxyService) {}

  @All('*')
  @ApiOperation({ summary: '代理所有API请求到后端服务' })
  @ApiResponse({ status: 200, description: '请求成功代理' })
  @ApiResponse({ status: 404, description: '路由未找到' })
  @ApiResponse({ status: 502, description: '网关错误' })
  @ApiResponse({ status: 503, description: '服务不可用' })
  async proxyRequest(@Req() req: Request, @Res() res: Response): Promise<void> {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] as string || this.generateRequestId();
    
    this.logger.log(`代理请求: ${req.method} ${req.path}`, {
      requestId,
      userAgent: req.get('user-agent'),
      ip: req.ip,
    });

    try {
      await this.proxyService.proxyRequest(req, res, {
        stripPath: true,
        preserveHost: false,
        followRedirects: true,
      });

      const duration = Date.now() - startTime;
      this.logger.log(`代理完成: ${req.method} ${req.path} (${duration}ms)`, {
        requestId,
        duration,
        status: res.statusCode,
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`代理失败: ${req.method} ${req.path} (${duration}ms)`, {
        requestId,
        duration,
        error: error.message,
      });

      // 如果响应还没有发送，发送错误响应
      if (!res.headersSent) {
        res.status(500).json({
          error: '代理请求失败',
          requestId,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
