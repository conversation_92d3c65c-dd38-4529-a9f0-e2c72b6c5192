"""
AI模型管理器
负责模型的加载、卸载、版本管理、负载均衡等核心功能
"""

import asyncio
import hashlib
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor

import torch
from transformers import AutoModel, AutoTokenizer, AutoConfig
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from ..config import get_settings
from ..core.database import get_async_session
from ..core.redis import get_redis_client
from ..core.storage import get_storage_client
from ..models.ai_model import AIModel, ModelStatus, ModelType, ModelFormat
from ..models.inference_log import InferenceLog, InferenceStatus
from .logger import logger


class ModelInstance:
    """模型实例"""
    
    def __init__(
        self,
        model_id: str,
        model: Any,
        tokenizer: Any = None,
        config: Dict = None,
        device: str = "cpu",
        memory_usage: float = 0.0
    ):
        self.model_id = model_id
        self.model = model
        self.tokenizer = tokenizer
        self.config = config or {}
        self.device = device
        self.memory_usage = memory_usage
        self.load_time = time.time()
        self.last_used = time.time()
        self.request_count = 0
        self.lock = threading.Lock()
    
    def update_usage(self):
        """更新使用统计"""
        with self.lock:
            self.last_used = time.time()
            self.request_count += 1
    
    @property
    def idle_time(self) -> float:
        """空闲时间（秒）"""
        return time.time() - self.last_used
    
    @property
    def uptime(self) -> float:
        """运行时间（秒）"""
        return time.time() - self.load_time


class ModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.loaded_models: Dict[str, ModelInstance] = {}
        self.loading_models: Dict[str, asyncio.Event] = {}
        self.model_queue = asyncio.Queue(maxsize=self.settings.inference_max_queue_size)
        self.executor = ThreadPoolExecutor(max_workers=self.settings.inference_worker_count)
        self.redis_client = None
        self.storage_client = None
        self._lock = asyncio.Lock()
        self._ready = False
        
        # GPU配置
        self.device_manager = DeviceManager()
        
        # 模型缓存目录
        self.cache_dir = Path(self.settings.model_cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """初始化模型管理器"""
        try:
            # 初始化Redis和存储客户端
            self.redis_client = await get_redis_client()
            self.storage_client = await get_storage_client()
            
            # 初始化设备管理器
            await self.device_manager.initialize()
            
            # 预加载模型（如果启用）
            if self.settings.model_warmup_enabled:
                await self._warmup_models()
            
            self._ready = True
            logger.info("模型管理器初始化完成")
            
        except Exception as e:
            logger.error(f"模型管理器初始化失败: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 卸载所有模型
            for model_id in list(self.loaded_models.keys()):
                await self.unload_model(model_id)
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            logger.info("模型管理器清理完成")
            
        except Exception as e:
            logger.error(f"模型管理器清理失败: {e}")
    
    def is_ready(self) -> bool:
        """检查是否就绪"""
        return self._ready
    
    async def load_model(self, model_id: str, force_reload: bool = False) -> bool:
        """加载模型"""
        async with self._lock:
            # 检查是否已加载
            if model_id in self.loaded_models and not force_reload:
                logger.info(f"模型 {model_id} 已加载")
                return True
            
            # 检查是否正在加载
            if model_id in self.loading_models:
                logger.info(f"模型 {model_id} 正在加载中，等待完成")
                await self.loading_models[model_id].wait()
                return model_id in self.loaded_models
            
            # 创建加载事件
            loading_event = asyncio.Event()
            self.loading_models[model_id] = loading_event
            
            try:
                # 获取模型信息
                async with get_async_session() as session:
                    result = await session.execute(
                        select(AIModel).where(AIModel.id == model_id)
                    )
                    model_record = result.scalar_one_or_none()
                    
                    if not model_record:
                        logger.error(f"模型 {model_id} 不存在")
                        return False
                    
                    if not model_record.is_ready:
                        logger.error(f"模型 {model_id} 状态不是就绪状态: {model_record.status}")
                        return False
                
                # 更新模型状态为加载中
                await self._update_model_status(model_id, ModelStatus.LOADING)
                
                # 检查内存使用
                if not await self._check_memory_availability(model_record.memory_usage_mb or 0):
                    logger.error(f"内存不足，无法加载模型 {model_id}")
                    await self._update_model_status(model_id, ModelStatus.READY)
                    return False
                
                # 下载模型文件（如果需要）
                model_path = await self._ensure_model_files(model_record)
                
                # 选择设备
                device = await self.device_manager.allocate_device(
                    model_record.requires_gpu,
                    model_record.gpu_memory_mb or 0
                )
                
                # 加载模型
                model_instance = await self._load_model_instance(
                    model_record, model_path, device
                )
                
                # 存储模型实例
                self.loaded_models[model_id] = model_instance
                
                # 更新模型状态
                await self._update_model_status(model_id, ModelStatus.LOADED)
                
                logger.info(f"模型 {model_id} 加载成功，设备: {device}")
                return True
                
            except Exception as e:
                logger.error(f"加载模型 {model_id} 失败: {e}")
                await self._update_model_status(model_id, ModelStatus.ERROR)
                return False
                
            finally:
                # 清理加载状态
                loading_event.set()
                self.loading_models.pop(model_id, None)
    
    async def unload_model(self, model_id: str) -> bool:
        """卸载模型"""
        async with self._lock:
            if model_id not in self.loaded_models:
                logger.warning(f"模型 {model_id} 未加载")
                return True
            
            try:
                model_instance = self.loaded_models[model_id]
                
                # 释放设备资源
                await self.device_manager.release_device(model_instance.device)
                
                # 清理模型
                del model_instance.model
                if model_instance.tokenizer:
                    del model_instance.tokenizer
                
                # 清理GPU缓存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                # 移除模型实例
                del self.loaded_models[model_id]
                
                # 更新模型状态
                await self._update_model_status(model_id, ModelStatus.READY)
                
                logger.info(f"模型 {model_id} 卸载成功")
                return True
                
            except Exception as e:
                logger.error(f"卸载模型 {model_id} 失败: {e}")
                return False
    
    async def predict(
        self,
        model_id: str,
        input_data: Dict[str, Any],
        request_id: str,
        user_id: str = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """执行预测"""
        start_time = time.time()
        
        # 检查模型是否加载
        if model_id not in self.loaded_models:
            if not await self.load_model(model_id):
                raise RuntimeError(f"无法加载模型 {model_id}")
        
        model_instance = self.loaded_models[model_id]
        model_instance.update_usage()
        
        # 创建推理日志
        inference_log = await self._create_inference_log(
            model_id, request_id, user_id, input_data
        )
        
        try:
            # 执行推理
            queue_start = time.time()
            await self.model_queue.put((model_instance, input_data, inference_log))
            queue_time = (time.time() - queue_start) * 1000
            
            # 在线程池中执行推理
            processing_start = time.time()
            output_data = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._run_inference,
                model_instance,
                input_data
            )
            processing_time = (time.time() - processing_start) * 1000
            total_time = (time.time() - start_time) * 1000
            
            # 更新推理日志
            await self._update_inference_log(
                inference_log.id,
                InferenceStatus.COMPLETED,
                output_data,
                queue_time,
                processing_time,
                total_time
            )
            
            # 返回结果和元数据
            metadata = {
                "model_id": model_id,
                "request_id": request_id,
                "processing_time_ms": processing_time,
                "total_time_ms": total_time,
                "device": model_instance.device,
            }
            
            return output_data, metadata
            
        except Exception as e:
            # 更新推理日志为失败
            await self._update_inference_log(
                inference_log.id,
                InferenceStatus.FAILED,
                error_message=str(e)
            )
            raise
        
        finally:
            # 从队列中移除
            try:
                self.model_queue.task_done()
            except ValueError:
                pass
    
    def _run_inference(self, model_instance: ModelInstance, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """在线程中运行推理"""
        try:
            # 这里根据模型类型执行不同的推理逻辑
            # 这是一个简化的示例，实际实现需要根据具体模型类型来处理
            
            if hasattr(model_instance.model, 'generate'):
                # 文本生成模型
                inputs = model_instance.tokenizer(
                    input_data.get('text', ''),
                    return_tensors='pt'
                ).to(model_instance.device)
                
                with torch.no_grad():
                    outputs = model_instance.model.generate(
                        **inputs,
                        max_length=input_data.get('max_length', 100),
                        temperature=input_data.get('temperature', 0.7),
                        do_sample=True
                    )
                
                generated_text = model_instance.tokenizer.decode(
                    outputs[0], skip_special_tokens=True
                )
                
                return {
                    'generated_text': generated_text,
                    'input_length': len(inputs['input_ids'][0]),
                    'output_length': len(outputs[0])
                }
            
            else:
                # 其他类型的模型
                # 这里需要根据具体模型类型实现
                return {'result': 'Model inference not implemented for this type'}
                
        except Exception as e:
            logger.error(f"推理执行失败: {e}")
            raise
    
    async def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        # 从数据库获取模型信息
        async with get_async_session() as session:
            result = await session.execute(
                select(AIModel).where(AIModel.id == model_id)
            )
            model_record = result.scalar_one_or_none()
            
            if not model_record:
                return None
            
            info = model_record.to_dict()
            
            # 添加运行时信息
            if model_id in self.loaded_models:
                model_instance = self.loaded_models[model_id]
                info.update({
                    'is_loaded': True,
                    'device': model_instance.device,
                    'memory_usage_mb': model_instance.memory_usage,
                    'uptime_seconds': model_instance.uptime,
                    'idle_time_seconds': model_instance.idle_time,
                    'request_count': model_instance.request_count,
                })
            else:
                info['is_loaded'] = False
            
            return info
    
    async def list_loaded_models(self) -> List[Dict[str, Any]]:
        """列出已加载的模型"""
        models = []
        for model_id, model_instance in self.loaded_models.items():
            models.append({
                'model_id': model_id,
                'device': model_instance.device,
                'memory_usage_mb': model_instance.memory_usage,
                'uptime_seconds': model_instance.uptime,
                'idle_time_seconds': model_instance.idle_time,
                'request_count': model_instance.request_count,
            })
        return models
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'loaded_models_count': len(self.loaded_models),
            'queue_size': self.model_queue.qsize(),
            'max_queue_size': self.settings.inference_max_queue_size,
            'worker_count': self.settings.inference_worker_count,
            'device_info': await self.device_manager.get_device_info(),
            'memory_info': await self._get_memory_info(),
        }
    
    # 私有方法
    async def _warmup_models(self) -> None:
        """预热模型"""
        # 这里可以实现模型预加载逻辑
        pass
    
    async def _check_memory_availability(self, required_memory_mb: float) -> bool:
        """检查内存可用性"""
        # 简化的内存检查，实际实现需要更复杂的逻辑
        return True
    
    async def _ensure_model_files(self, model_record: AIModel) -> str:
        """确保模型文件存在"""
        # 这里实现模型文件下载逻辑
        return str(self.cache_dir / model_record.name)
    
    async def _load_model_instance(
        self, model_record: AIModel, model_path: str, device: str
    ) -> ModelInstance:
        """加载模型实例"""
        # 这里根据模型格式加载模型
        if model_record.model_format == ModelFormat.HUGGINGFACE:
            model = AutoModel.from_pretrained(model_path)
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model.to(device)
        else:
            # 其他格式的模型加载逻辑
            raise NotImplementedError(f"不支持的模型格式: {model_record.model_format}")
        
        return ModelInstance(
            model_id=str(model_record.id),
            model=model,
            tokenizer=tokenizer,
            device=device,
            memory_usage=model_record.memory_usage_mb or 0
        )
    
    async def _update_model_status(self, model_id: str, status: ModelStatus) -> None:
        """更新模型状态"""
        async with get_async_session() as session:
            result = await session.execute(
                select(AIModel).where(AIModel.id == model_id)
            )
            model = result.scalar_one_or_none()
            if model:
                model.status = status
                await session.commit()
    
    async def _create_inference_log(
        self, model_id: str, request_id: str, user_id: str, input_data: Dict
    ) -> InferenceLog:
        """创建推理日志"""
        async with get_async_session() as session:
            log = InferenceLog(
                request_id=request_id,
                model_id=model_id,
                user_id=user_id,
                input_data=input_data,
                status=InferenceStatus.PENDING
            )
            session.add(log)
            await session.commit()
            await session.refresh(log)
            return log
    
    async def _update_inference_log(
        self,
        log_id: str,
        status: InferenceStatus,
        output_data: Dict = None,
        queue_time_ms: float = None,
        processing_time_ms: float = None,
        total_time_ms: float = None,
        error_message: str = None
    ) -> None:
        """更新推理日志"""
        async with get_async_session() as session:
            result = await session.execute(
                select(InferenceLog).where(InferenceLog.id == log_id)
            )
            log = result.scalar_one_or_none()
            if log:
                log.status = status
                if output_data:
                    log.output_data = output_data
                if queue_time_ms:
                    log.queue_time_ms = queue_time_ms
                if processing_time_ms:
                    log.processing_time_ms = processing_time_ms
                if total_time_ms:
                    log.total_time_ms = total_time_ms
                if error_message:
                    log.error_message = error_message
                if status == InferenceStatus.COMPLETED:
                    log.completed_at = asyncio.get_event_loop().time()
                await session.commit()
    
    async def _get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        import psutil
        memory = psutil.virtual_memory()
        return {
            'total_mb': memory.total / 1024 / 1024,
            'available_mb': memory.available / 1024 / 1024,
            'used_mb': memory.used / 1024 / 1024,
            'percent': memory.percent,
        }


class DeviceManager:
    """设备管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.devices = []
        self.device_usage = {}
    
    async def initialize(self) -> None:
        """初始化设备管理器"""
        # 检测可用设备
        if torch.cuda.is_available() and self.settings.gpu_enabled:
            for device_id in self.settings.gpu_device_ids:
                if device_id < torch.cuda.device_count():
                    device = f"cuda:{device_id}"
                    self.devices.append(device)
                    self.device_usage[device] = 0
        
        # 添加CPU设备
        self.devices.append("cpu")
        self.device_usage["cpu"] = 0
        
        logger.info(f"可用设备: {self.devices}")
    
    async def allocate_device(self, requires_gpu: bool, memory_mb: float) -> str:
        """分配设备"""
        if requires_gpu and any(d.startswith("cuda") for d in self.devices):
            # 选择GPU使用率最低的设备
            gpu_devices = [d for d in self.devices if d.startswith("cuda")]
            device = min(gpu_devices, key=lambda d: self.device_usage[d])
        else:
            device = "cpu"
        
        self.device_usage[device] += memory_mb
        return device
    
    async def release_device(self, device: str) -> None:
        """释放设备"""
        if device in self.device_usage:
            self.device_usage[device] = max(0, self.device_usage[device])
    
    async def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        info = {
            'available_devices': self.devices,
            'device_usage': self.device_usage,
        }
        
        if torch.cuda.is_available():
            info['cuda_devices'] = []
            for i in range(torch.cuda.device_count()):
                device_info = {
                    'device_id': i,
                    'name': torch.cuda.get_device_name(i),
                    'memory_total_mb': torch.cuda.get_device_properties(i).total_memory / 1024 / 1024,
                    'memory_allocated_mb': torch.cuda.memory_allocated(i) / 1024 / 1024,
                    'memory_cached_mb': torch.cuda.memory_reserved(i) / 1024 / 1024,
                }
                info['cuda_devices'].append(device_info)
        
        return info
