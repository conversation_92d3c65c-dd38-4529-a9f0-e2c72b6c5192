"""
数据库连接和管理模块
"""

import asyncio
from typing import AsyncGenerator, Optional

from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool, QueuePool

from ..config import get_settings
from .logger import logger

# 创建基础模型类
Base = declarative_base()

# 全局变量
async_engine = None
async_session_factory = None
sync_engine = None
sync_session_factory = None


async def init_db() -> None:
    """初始化数据库连接"""
    global async_engine, async_session_factory, sync_engine, sync_session_factory
    
    settings = get_settings()
    
    try:
        # 创建异步引擎
        async_database_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")
        async_engine = create_async_engine(
            async_database_url,
            poolclass=Que<PERSON>Pool,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=settings.debug,
        )
        
        # 创建异步会话工厂
        async_session_factory = async_sessionmaker(
            async_engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        # 创建同步引擎（用于Alembic迁移）
        sync_engine = create_engine(
            settings.database_url,
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=settings.debug,
        )
        
        # 创建同步会话工厂
        sync_session_factory = sessionmaker(
            sync_engine,
            expire_on_commit=False,
        )
        
        # 测试连接
        async with async_engine.begin() as conn:
            await conn.execute("SELECT 1")
        
        logger.info("数据库连接初始化成功")
        
    except Exception as e:
        logger.error(f"数据库连接初始化失败: {e}")
        raise


async def close_db() -> None:
    """关闭数据库连接"""
    global async_engine, sync_engine
    
    try:
        if async_engine:
            await async_engine.dispose()
            logger.info("异步数据库连接已关闭")
        
        if sync_engine:
            sync_engine.dispose()
            logger.info("同步数据库连接已关闭")
            
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    if not async_session_factory:
        raise RuntimeError("数据库未初始化")
    
    async with async_session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_sync_session():
    """获取同步数据库会话"""
    if not sync_session_factory:
        raise RuntimeError("数据库未初始化")
    
    return sync_session_factory()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.async_engine = None
        self.async_session_factory = None
        self.sync_engine = None
        self.sync_session_factory = None
    
    async def initialize(self) -> None:
        """初始化数据库管理器"""
        await init_db()
        self.async_engine = async_engine
        self.async_session_factory = async_session_factory
        self.sync_engine = sync_engine
        self.sync_session_factory = sync_session_factory
    
    async def cleanup(self) -> None:
        """清理数据库连接"""
        await close_db()
    
    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            if not self.async_engine:
                return False
            
            async with self.async_engine.begin() as conn:
                await conn.execute("SELECT 1")
            return True
            
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        if not self.async_session_factory:
            raise RuntimeError("数据库未初始化")
        
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def execute_query(self, query: str, params: dict = None) -> list:
        """执行查询"""
        async with self.get_session() as session:
            result = await session.execute(query, params or {})
            return result.fetchall()
    
    async def get_connection_info(self) -> dict:
        """获取连接信息"""
        if not self.async_engine:
            return {"status": "disconnected"}
        
        pool = self.async_engine.pool
        return {
            "status": "connected",
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 数据库事件监听器
@event.listens_for(sync_engine, "connect", once=True)
def set_sqlite_pragma(dbapi_connection, connection_record):
    """设置数据库连接参数"""
    if "sqlite" in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()


@event.listens_for(sync_engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """记录SQL执行前的信息"""
    if get_settings().debug:
        logger.debug(f"SQL执行: {statement[:100]}...")


@event.listens_for(sync_engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """记录SQL执行后的信息"""
    if get_settings().debug:
        total = context._sa_total_time = context.get_execution_options().get("total_time", 0)
        logger.debug(f"SQL执行完成，耗时: {total:.3f}s")


# 依赖注入函数
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI依赖注入：获取数据库会话"""
    async for session in get_async_session():
        yield session
