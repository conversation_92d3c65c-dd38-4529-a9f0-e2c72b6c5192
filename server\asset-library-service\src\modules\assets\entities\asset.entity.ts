import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  ManyToMany,
  OneToMany,
  JoinTable,
  Index,
} from 'typeorm';
import { Category } from '../../categories/entities/category.entity';
import { Tag } from '../../tags/entities/tag.entity';
import { AssetVersion } from '../../versions/entities/asset-version.entity';
import { User } from '../../auth/entities/user.entity';

export enum AssetType {
  MODEL_3D = '3d_model',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  SCRIPT = 'script',
  SCENE = 'scene',
}

export enum AssetStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ARCHIVED = 'archived',
}

export enum AssetLicense {
  FREE = 'free',
  COMMERCIAL = 'commercial',
  RESTRICTED = 'restricted',
  CUSTOM = 'custom',
}

@Entity('assets')
@Index(['name', 'type'])
@Index(['status', 'createdAt'])
@Index(['category', 'type'])
export class Asset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AssetType,
    default: AssetType.MODEL_3D,
  })
  @Index()
  type: AssetType;

  @Column({
    type: 'enum',
    enum: AssetStatus,
    default: AssetStatus.DRAFT,
  })
  @Index()
  status: AssetStatus;

  @Column({
    type: 'enum',
    enum: AssetLicense,
    default: AssetLicense.FREE,
  })
  license: AssetLicense;

  // 文件信息
  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  @Column({ name: 'file_format', length: 50 })
  fileFormat: string;

  @Column({ name: 'mime_type', length: 100 })
  mimeType: string;

  // 缩略图
  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath: string;

  @Column({ name: 'preview_path', nullable: true })
  previewPath: string;

  // 元数据
  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  // 统计信息
  @Column({ name: 'download_count', default: 0 })
  downloadCount: number;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'like_count', default: 0 })
  likeCount: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  rating: number;

  @Column({ name: 'rating_count', default: 0 })
  ratingCount: number;

  // 版本信息
  @Column({ name: 'current_version', default: '1.0.0' })
  currentVersion: string;

  @Column({ name: 'is_latest_version', default: true })
  isLatestVersion: boolean;

  // 关联关系
  @ManyToOne(() => Category, category => category.assets, { eager: true })
  category: Category;

  @ManyToMany(() => Tag, tag => tag.assets, { eager: true })
  @JoinTable({
    name: 'asset_tags',
    joinColumn: { name: 'asset_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tag_id', referencedColumnName: 'id' },
  })
  tags: Tag[];

  @OneToMany(() => AssetVersion, version => version.asset)
  versions: AssetVersion[];

  @ManyToOne(() => User, user => user.assets)
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  reviewer: User;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'published_at', nullable: true })
  publishedAt: Date;

  // 软删除
  @Column({ name: 'deleted_at', nullable: true })
  deletedAt: Date;

  // 计算属性
  get isPublished(): boolean {
    return this.status === AssetStatus.APPROVED && this.publishedAt !== null;
  }

  get fileExtension(): string {
    return this.filePath.split('.').pop()?.toLowerCase() || '';
  }

  get fileSizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = Number(this.fileSize);
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}
