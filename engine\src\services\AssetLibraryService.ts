/**
 * 资源库服务
 * 管理3D资产的存储、分类、检索和版本控制
 */
import { EventEmitter } from '../utils/EventEmitter';
import { DatabaseManager } from '../database/DatabaseManager';
import { FileStorageManager } from '../storage/FileStorageManager';

/**
 * 资产元数据
 */
export interface AssetMetadata {
  /** 资产ID */
  id: string;
  /** 资产名称 */
  name: string;
  /** 资产类型 */
  type: 'model' | 'texture' | 'material' | 'environment' | 'animation';
  /** 分类 */
  category: string;
  /** 子分类 */
  subcategory?: string;
  /** 标签 */
  tags: string[];
  /** 描述 */
  description: string;
  /** 文件路径 */
  filePath: string;
  /** 文件大小 */
  fileSize: number;
  /** 文件格式 */
  format: string;
  /** 版本号 */
  version: string;
  /** 创建者 */
  creator: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 许可证 */
  license: string;
  /** 预览图 */
  thumbnail?: string;
  /** 技术参数 */
  technicalSpecs: {
    polygonCount?: number;
    textureResolution?: string;
    animationDuration?: number;
    fileFormat: string;
  };
  /** 使用统计 */
  usageStats: {
    downloadCount: number;
    usageCount: number;
    rating: number;
    reviewCount: number;
  };
}

/**
 * 资产搜索条件
 */
export interface AssetSearchCriteria {
  /** 关键词 */
  keywords?: string;
  /** 类型过滤 */
  type?: string;
  /** 分类过滤 */
  category?: string;
  /** 标签过滤 */
  tags?: string[];
  /** 创建者过滤 */
  creator?: string;
  /** 许可证过滤 */
  license?: string;
  /** 文件格式过滤 */
  format?: string;
  /** 排序方式 */
  sortBy?: 'name' | 'createdAt' | 'rating' | 'usageCount';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 分页 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 资产上传配置
 */
export interface AssetUploadConfig {
  /** 允许的文件类型 */
  allowedTypes: string[];
  /** 最大文件大小 */
  maxFileSize: number;
  /** 是否自动生成缩略图 */
  generateThumbnail: boolean;
  /** 是否自动提取元数据 */
  extractMetadata: boolean;
  /** 存储路径 */
  storagePath: string;
}

/**
 * 资源库服务
 */
export class AssetLibraryService extends EventEmitter {
  private dbManager: DatabaseManager;
  private storageManager: FileStorageManager;
  private uploadConfig: AssetUploadConfig;
  private assetCache: Map<string, AssetMetadata> = new Map();

  constructor() {
    super();
    this.dbManager = new DatabaseManager();
    this.storageManager = new FileStorageManager();
    this.uploadConfig = {
      allowedTypes: ['.glb', '.gltf', '.fbx', '.obj', '.jpg', '.png', '.hdr'],
      maxFileSize: 100 * 1024 * 1024, // 100MB
      generateThumbnail: true,
      extractMetadata: true,
      storagePath: './assets'
    };
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    await this.dbManager.connect();
    await this.storageManager.initialize();
    await this.loadAssetIndex();
    
    console.log('资源库服务初始化完成');
  }

  /**
   * 上传资产
   */
  async uploadAsset(
    file: File | Buffer,
    metadata: Partial<AssetMetadata>,
    userId: string
  ): Promise<AssetMetadata> {
    try {
      // 验证文件
      this.validateFile(file);

      // 生成资产ID
      const assetId = this.generateAssetId();

      // 存储文件
      const filePath = await this.storageManager.saveFile(file, assetId);

      // 提取技术参数
      const technicalSpecs = await this.extractTechnicalSpecs(file, filePath);

      // 生成缩略图
      const thumbnail = this.uploadConfig.generateThumbnail 
        ? await this.generateThumbnail(filePath)
        : undefined;

      // 创建完整元数据
      const completeMetadata: AssetMetadata = {
        id: assetId,
        name: metadata.name || 'Untitled Asset',
        type: metadata.type || 'model',
        category: metadata.category || 'misc',
        subcategory: metadata.subcategory,
        tags: metadata.tags || [],
        description: metadata.description || '',
        filePath,
        fileSize: this.getFileSize(file),
        format: this.getFileFormat(file),
        version: '1.0.0',
        creator: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        license: metadata.license || 'standard',
        thumbnail,
        technicalSpecs,
        usageStats: {
          downloadCount: 0,
          usageCount: 0,
          rating: 0,
          reviewCount: 0
        }
      };

      // 保存到数据库
      await this.dbManager.saveAsset(completeMetadata);

      // 更新缓存
      this.assetCache.set(assetId, completeMetadata);

      // 发出事件
      this.emit('assetUploaded', completeMetadata);

      return completeMetadata;
    } catch (error) {
      console.error('资产上传失败:', error);
      throw error;
    }
  }

  /**
   * 搜索资产
   */
  async searchAssets(criteria: AssetSearchCriteria): Promise<{
    assets: AssetMetadata[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const result = await this.dbManager.searchAssets(criteria);
      
      // 更新使用统计
      result.assets.forEach(asset => {
        this.updateUsageStats(asset.id, 'search');
      });

      return result;
    } catch (error) {
      console.error('资产搜索失败:', error);
      throw error;
    }
  }

  /**
   * 获取资产详情
   */
  async getAsset(assetId: string): Promise<AssetMetadata | null> {
    try {
      // 先从缓存获取
      if (this.assetCache.has(assetId)) {
        return this.assetCache.get(assetId)!;
      }

      // 从数据库获取
      const asset = await this.dbManager.getAsset(assetId);
      if (asset) {
        this.assetCache.set(assetId, asset);
      }

      return asset;
    } catch (error) {
      console.error('获取资产失败:', error);
      return null;
    }
  }

  /**
   * 下载资产
   */
  async downloadAsset(assetId: string, userId: string): Promise<Buffer> {
    try {
      const asset = await this.getAsset(assetId);
      if (!asset) {
        throw new Error('资产不存在');
      }

      // 检查权限
      await this.checkDownloadPermission(assetId, userId);

      // 获取文件
      const fileBuffer = await this.storageManager.getFile(asset.filePath);

      // 更新下载统计
      await this.updateUsageStats(assetId, 'download');

      // 发出事件
      this.emit('assetDownloaded', { assetId, userId });

      return fileBuffer;
    } catch (error) {
      console.error('资产下载失败:', error);
      throw error;
    }
  }

  /**
   * 删除资产
   */
  async deleteAsset(assetId: string, userId: string): Promise<void> {
    try {
      const asset = await this.getAsset(assetId);
      if (!asset) {
        throw new Error('资产不存在');
      }

      // 检查权限
      await this.checkDeletePermission(assetId, userId);

      // 删除文件
      await this.storageManager.deleteFile(asset.filePath);
      if (asset.thumbnail) {
        await this.storageManager.deleteFile(asset.thumbnail);
      }

      // 从数据库删除
      await this.dbManager.deleteAsset(assetId);

      // 从缓存删除
      this.assetCache.delete(assetId);

      // 发出事件
      this.emit('assetDeleted', { assetId, userId });
    } catch (error) {
      console.error('资产删除失败:', error);
      throw error;
    }
  }

  /**
   * 更新资产元数据
   */
  async updateAsset(
    assetId: string,
    updates: Partial<AssetMetadata>,
    userId: string
  ): Promise<AssetMetadata> {
    try {
      const asset = await this.getAsset(assetId);
      if (!asset) {
        throw new Error('资产不存在');
      }

      // 检查权限
      await this.checkUpdatePermission(assetId, userId);

      // 更新元数据
      const updatedAsset: AssetMetadata = {
        ...asset,
        ...updates,
        updatedAt: new Date()
      };

      // 保存到数据库
      await this.dbManager.updateAsset(assetId, updatedAsset);

      // 更新缓存
      this.assetCache.set(assetId, updatedAsset);

      // 发出事件
      this.emit('assetUpdated', updatedAsset);

      return updatedAsset;
    } catch (error) {
      console.error('资产更新失败:', error);
      throw error;
    }
  }

  /**
   * 获取资产分类
   */
  async getCategories(): Promise<{ category: string; count: number }[]> {
    try {
      return await this.dbManager.getAssetCategories();
    } catch (error) {
      console.error('获取分类失败:', error);
      return [];
    }
  }

  /**
   * 获取热门资产
   */
  async getPopularAssets(limit: number = 10): Promise<AssetMetadata[]> {
    try {
      return await this.dbManager.getPopularAssets(limit);
    } catch (error) {
      console.error('获取热门资产失败:', error);
      return [];
    }
  }

  /**
   * 验证文件
   */
  private validateFile(file: File | Buffer): void {
    const fileSize = this.getFileSize(file);
    const fileFormat = this.getFileFormat(file);

    if (fileSize > this.uploadConfig.maxFileSize) {
      throw new Error('文件大小超过限制');
    }

    if (!this.uploadConfig.allowedTypes.includes(fileFormat)) {
      throw new Error('不支持的文件格式');
    }
  }

  /**
   * 生成资产ID
   */
  private generateAssetId(): string {
    return `asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取文件大小
   */
  private getFileSize(file: File | Buffer): number {
    if (file instanceof File) {
      return file.size;
    }
    return file.length;
  }

  /**
   * 获取文件格式
   */
  private getFileFormat(file: File | Buffer): string {
    if (file instanceof File) {
      return '.' + file.name.split('.').pop()?.toLowerCase();
    }
    // 对于Buffer，需要其他方式检测格式
    return '.unknown';
  }

  /**
   * 提取技术参数
   */
  private async extractTechnicalSpecs(file: File | Buffer, filePath: string): Promise<any> {
    // 这里应该实现具体的文件分析逻辑
    return {
      fileFormat: this.getFileFormat(file),
      polygonCount: 0, // 需要实际分析
      textureResolution: 'unknown'
    };
  }

  /**
   * 生成缩略图
   */
  private async generateThumbnail(filePath: string): Promise<string | undefined> {
    // 这里应该实现缩略图生成逻辑
    return undefined;
  }

  /**
   * 更新使用统计
   */
  private async updateUsageStats(assetId: string, action: 'search' | 'download' | 'use'): Promise<void> {
    try {
      await this.dbManager.updateAssetStats(assetId, action);
    } catch (error) {
      console.error('更新统计失败:', error);
    }
  }

  /**
   * 检查下载权限
   */
  private async checkDownloadPermission(assetId: string, userId: string): Promise<void> {
    // 实现权限检查逻辑
  }

  /**
   * 检查删除权限
   */
  private async checkDeletePermission(assetId: string, userId: string): Promise<void> {
    // 实现权限检查逻辑
  }

  /**
   * 检查更新权限
   */
  private async checkUpdatePermission(assetId: string, userId: string): Promise<void> {
    // 实现权限检查逻辑
  }

  /**
   * 加载资产索引
   */
  private async loadAssetIndex(): Promise<void> {
    try {
      const assets = await this.dbManager.getAllAssets();
      assets.forEach(asset => {
        this.assetCache.set(asset.id, asset);
      });
      console.log(`加载了 ${assets.length} 个资产到缓存`);
    } catch (error) {
      console.error('加载资产索引失败:', error);
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    await this.dbManager.disconnect();
    this.assetCache.clear();
    this.removeAllListeners();
  }
}
