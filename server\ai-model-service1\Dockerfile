# 多阶段构建 - 基础镜像
FROM python:3.11-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt pyproject.toml ./

# 安装Python依赖
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# 开发阶段
FROM base as development

# 安装开发依赖
RUN pip install pytest pytest-asyncio black isort flake8 mypy

# 复制源代码
COPY . .

# 设置权限
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8002

# 启动命令
CMD ["python", "-m", "uvicorn", "src.ai_model_service.main:create_app", "--host", "0.0.0.0", "--port", "8002", "--reload"]

# 生产阶段
FROM base as production

# 复制源代码
COPY src/ ./src/
COPY alembic/ ./alembic/
COPY alembic.ini ./

# 设置权限
RUN chown -R appuser:appuser /app

# 创建模型缓存目录
RUN mkdir -p /app/models && chown -R appuser:appuser /app/models

# 创建日志目录
RUN mkdir -p /app/logs && chown -R appuser:appuser /app/logs

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "src.ai_model_service.main:create_app", "--host", "0.0.0.0", "--port", "8002", "--workers", "1"]

# GPU版本
FROM nvidia/cuda:11.8-runtime-ubuntu20.04 as gpu-production

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    NVIDIA_VISIBLE_DEVICES=all \
    NVIDIA_DRIVER_CAPABILITIES=compute,utility

# 安装Python和系统依赖
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3-pip \
    build-essential \
    curl \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建符号链接
RUN ln -s /usr/bin/python3.11 /usr/bin/python

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt pyproject.toml ./

# 安装Python依赖（包括GPU版本）
RUN pip install --upgrade pip && \
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 && \
    pip install -r requirements.txt

# 复制源代码
COPY src/ ./src/
COPY alembic/ ./alembic/
COPY alembic.ini ./

# 设置权限
RUN chown -R appuser:appuser /app

# 创建目录
RUN mkdir -p /app/models /app/logs && chown -R appuser:appuser /app/models /app/logs

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "src.ai_model_service.main:create_app", "--host", "0.0.0.0", "--port", "8002", "--workers", "1"]
