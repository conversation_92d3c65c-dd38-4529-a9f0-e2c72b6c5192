import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CacheService } from '../services/cache.service';
import { StorageService } from '../services/storage.service';

interface HealthStatus {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: 'ok' | 'error';
    redis: 'ok' | 'error';
    minio: 'ok' | 'error';
    elasticsearch: 'ok' | 'error';
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
}

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly cacheService: CacheService,
    private readonly storageService: StorageService,
  ) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({
    status: 200,
    description: '服务健康状态',
  })
  async getHealth(): Promise<HealthStatus> {
    const memoryUsage = process.memoryUsage();
    
    // 检查各个服务的状态
    const services = {
      database: 'ok' as 'ok' | 'error',
      redis: this.cacheService.getConnectionStatus() ? 'ok' as const : 'error' as const,
      minio: this.storageService.isStorageConnected() ? 'ok' as const : 'error' as const,
      elasticsearch: 'ok' as 'ok' | 'error', // 这里可以添加ES连接检查
    };

    // 判断整体状态
    const hasError = Object.values(services).some(status => status === 'error');
    
    return {
      status: hasError ? 'error' : 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services,
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
      },
    };
  }

  @Get('ready')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({
    status: 200,
    description: '服务就绪状态',
  })
  async getReady(): Promise<{ ready: boolean }> {
    // 检查关键服务是否就绪
    const redisReady = this.cacheService.getConnectionStatus();
    const minioReady = this.storageService.isStorageConnected();
    
    return {
      ready: redisReady && minioReady,
    };
  }

  @Get('live')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({
    status: 200,
    description: '服务存活状态',
  })
  async getLive(): Promise<{ alive: boolean }> {
    return {
      alive: true,
    };
  }
}
