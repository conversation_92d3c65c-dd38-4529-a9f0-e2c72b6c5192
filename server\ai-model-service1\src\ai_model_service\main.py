"""
AI模型微服务主入口
支持模型注册、版本管理、负载均衡、模型热更新和性能监控
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse

from .config import get_settings
from .core.database import init_db
from .core.redis import init_redis
from .core.storage import init_storage
from .core.model_manager import ModelManager
from .core.logger import setup_logging
from .api.v1 import api_router
from .middleware.rate_limit import RateLimitMiddleware
from .middleware.metrics import MetricsMiddleware
from .middleware.request_id import RequestIDMiddleware

# 设置日志
logger = setup_logging()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理"""
    settings = get_settings()
    
    try:
        # 启动时初始化
        logger.info("正在启动AI模型微服务...")
        
        # 初始化数据库
        await init_db()
        logger.info("数据库初始化完成")
        
        # 初始化Redis
        await init_redis()
        logger.info("Redis初始化完成")
        
        # 初始化存储
        await init_storage()
        logger.info("存储服务初始化完成")
        
        # 初始化模型管理器
        model_manager = ModelManager()
        await model_manager.initialize()
        app.state.model_manager = model_manager
        logger.info("模型管理器初始化完成")
        
        logger.info(f"AI模型微服务启动成功，端口: {settings.port}")
        
        yield
        
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("正在关闭AI模型微服务...")
        
        if hasattr(app.state, 'model_manager'):
            await app.state.model_manager.cleanup()
            logger.info("模型管理器清理完成")
        
        logger.info("AI模型微服务关闭完成")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    app = FastAPI(
        title="AI模型微服务",
        description="支持模型注册、版本管理、负载均衡、模型热更新和性能监控的AI模型管理服务",
        version="1.0.0",
        docs_url="/api/docs" if settings.environment != "production" else None,
        redoc_url="/api/redoc" if settings.environment != "production" else None,
        lifespan=lifespan,
    )
    
    # 添加中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(MetricsMiddleware)
    app.add_middleware(RateLimitMiddleware)
    
    # 添加路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "内部服务器错误",
                "message": "服务暂时不可用，请稍后重试",
                "request_id": getattr(request.state, "request_id", None),
            }
        )
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "ok",
            "service": "ai-model-service",
            "version": "1.0.0",
            "timestamp": asyncio.get_event_loop().time(),
        }
    
    @app.get("/health/ready")
    async def readiness_check():
        """就绪检查"""
        try:
            # 检查关键服务是否就绪
            model_manager = getattr(app.state, 'model_manager', None)
            if not model_manager or not model_manager.is_ready():
                return JSONResponse(
                    status_code=503,
                    content={"ready": False, "reason": "模型管理器未就绪"}
                )
            
            return {"ready": True}
        except Exception as e:
            logger.error(f"就绪检查失败: {e}")
            return JSONResponse(
                status_code=503,
                content={"ready": False, "reason": str(e)}
            )
    
    @app.get("/health/live")
    async def liveness_check():
        """存活检查"""
        return {"alive": True}
    
    return app


def setup_signal_handlers() -> None:
    """设置信号处理器"""
    def signal_handler(signum: int, frame) -> None:
        logger.info(f"收到信号 {signum}，正在优雅关闭...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main() -> None:
    """主函数"""
    settings = get_settings()
    
    # 设置信号处理器
    setup_signal_handlers()
    
    # 创建应用
    app = create_app()
    
    # 启动服务
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        access_log=settings.environment != "production",
        reload=settings.environment == "development",
        workers=1,  # 由于模型加载的复杂性，使用单进程
    )


if __name__ == "__main__":
    main()
