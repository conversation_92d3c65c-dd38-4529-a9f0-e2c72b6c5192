version: '3.8'

services:
  # 基础设施服务
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: microservices
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - microservices

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - microservices

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - microservices

  elasticsearch:
    image: elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - microservices

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - microservices

  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - microservices

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - microservices

  # 服务注册中心
  service-registry:
    build:
      context: ./service-registry
      dockerfile: Dockerfile
    ports:
      - "8010:8010"
    environment:
      - NODE_ENV=production
      - PORT=8010
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/microservices
    depends_on:
      - postgres
      - redis
    networks:
      - microservices

  # API网关
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - PORT=8000
      - SERVICE_REGISTRY_URL=http://service-registry:8010
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/microservices
      - USER_SERVICE_URL=http://user-service:8001
      - PROJECT_SERVICE_URL=http://project-service:8006
      - ASSET_LIBRARY_SERVICE_URL=http://asset-library-service:8003
      - SCENE_TEMPLATE_SERVICE_URL=http://scene-template-service:8004
      - AI_MODEL_SERVICE_URL=http://ai-model-service:8002
      - SCENE_GENERATION_SERVICE_URL=http://scene-generation-service:8005
      - RENDER_SERVICE_URL=http://render-service:8007
      - KNOWLEDGE_SERVICE_URL=http://knowledge-service:8008
      - RAG_ENGINE_URL=http://rag-engine:8009
    depends_on:
      - service-registry
      - postgres
      - redis
    networks:
      - microservices

  # 用户服务
  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - NODE_ENV=production
      - PORT=8001
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - service-registry
    networks:
      - microservices

  # AI模型服务
  ai-model-service:
    build:
      context: ./ai-model-service
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - NODE_ENV=production
      - PORT=8002
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - minio
      - service-registry
    networks:
      - microservices

  # 资源库服务
  asset-library-service:
    build:
      context: ./asset-library-service
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    environment:
      - NODE_ENV=production
      - PORT=8003
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - minio
      - elasticsearch
      - service-registry
    networks:
      - microservices

  # 场景模板服务
  scene-template-service:
    build:
      context: ./scene-template-service
      dockerfile: Dockerfile
    ports:
      - "8004:8004"
    environment:
      - NODE_ENV=production
      - PORT=8004
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - minio
      - service-registry
    networks:
      - microservices

  # 场景生成服务
  scene-generation-service:
    build:
      context: ./scene-generation-service
      dockerfile: Dockerfile
    ports:
      - "8005:8005"
    environment:
      - NODE_ENV=production
      - PORT=8005
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - SERVICE_REGISTRY_URL=http://service-registry:8010
      - AI_MODEL_SERVICE_URL=http://ai-model-service:8002
      - ASSET_LIBRARY_SERVICE_URL=http://asset-library-service:8003
      - SCENE_TEMPLATE_SERVICE_URL=http://scene-template-service:8004
    depends_on:
      - postgres
      - redis
      - minio
      - service-registry
      - ai-model-service
      - asset-library-service
      - scene-template-service
    networks:
      - microservices

  # 项目服务
  project-service:
    build:
      context: ./project-service
      dockerfile: Dockerfile
    ports:
      - "8006:8006"
    environment:
      - NODE_ENV=production
      - PORT=8006
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - service-registry
    networks:
      - microservices

  # 渲染服务
  render-service:
    build:
      context: ./render-service
      dockerfile: Dockerfile
    ports:
      - "8007:8007"
    environment:
      - NODE_ENV=production
      - PORT=8007
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - minio
      - service-registry
    networks:
      - microservices

  # 知识库服务
  knowledge-service:
    build:
      context: ./knowledge-service
      dockerfile: Dockerfile
    ports:
      - "8008:8008"
    environment:
      - NODE_ENV=production
      - PORT=8008
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - SERVICE_REGISTRY_URL=http://service-registry:8010
    depends_on:
      - postgres
      - redis
      - elasticsearch
      - service-registry
    networks:
      - microservices

  # RAG引擎
  rag-engine:
    build:
      context: ./rag-engine
      dockerfile: Dockerfile
    ports:
      - "8009:8009"
    environment:
      - NODE_ENV=production
      - PORT=8009
      - DATABASE_URL=********************************************/microservices
      - REDIS_URL=redis://redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - SERVICE_REGISTRY_URL=http://service-registry:8010
      - KNOWLEDGE_SERVICE_URL=http://knowledge-service:8008
    depends_on:
      - postgres
      - redis
      - elasticsearch
      - service-registry
      - knowledge-service
    networks:
      - microservices

volumes:
  postgres_data:
  redis_data:
  minio_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  microservices:
    driver: bridge
