"""
推理日志数据模型
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, Float, Integer, 
    JSON, String, Text, ForeignKey, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..core.database import Base


class InferenceStatus(str, Enum):
    """推理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class InferenceLog(Base):
    """推理日志表"""
    __tablename__ = "inference_logs"
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    request_id = Column(String(255), nullable=False, index=True)
    model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id"), nullable=False)
    deployment_id = Column(UUID(as_uuid=True), ForeignKey("model_deployments.id"), nullable=True)
    
    # 请求信息
    user_id = Column(String(255), nullable=True, index=True)
    client_ip = Column(String(45), nullable=True)  # IPv6支持
    user_agent = Column(String(500), nullable=True)
    
    # 推理信息
    status = Column(SQLEnum(InferenceStatus), nullable=False, default=InferenceStatus.PENDING, index=True)
    input_data = Column(JSON, nullable=True)  # 输入数据（可能很大，考虑存储到对象存储）
    output_data = Column(JSON, nullable=True)  # 输出数据
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)
    
    # 性能指标
    queue_time_ms = Column(Float, nullable=True)  # 排队时间
    processing_time_ms = Column(Float, nullable=True)  # 处理时间
    total_time_ms = Column(Float, nullable=True)  # 总时间
    
    # 资源使用
    memory_usage_mb = Column(Float, nullable=True)
    gpu_memory_usage_mb = Column(Float, nullable=True)
    cpu_usage_percent = Column(Float, nullable=True)
    
    # 数据大小
    input_size_bytes = Column(Integer, nullable=True)
    output_size_bytes = Column(Integer, nullable=True)
    
    # 模型信息快照
    model_version = Column(String(50), nullable=True)
    model_type = Column(String(50), nullable=True)
    device_used = Column(String(50), nullable=True)
    
    # 质量指标
    confidence_score = Column(Float, nullable=True)  # 置信度
    quality_score = Column(Float, nullable=True)  # 质量分数
    
    # 缓存信息
    cache_hit = Column(Boolean, default=False)
    cache_key = Column(String(255), nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    model = relationship("AIModel", back_populates="inference_logs")
    deployment = relationship("ModelDeployment")
    
    # 索引
    __table_args__ = (
        Index("idx_model_status_created", "model_id", "status", "created_at"),
        Index("idx_user_created", "user_id", "created_at"),
        Index("idx_request_id", "request_id"),
        Index("idx_created_at", "created_at"),
    )
    
    def __repr__(self):
        return f"<InferenceLog(id={self.id}, request_id={self.request_id}, status={self.status})>"
    
    @property
    def is_completed(self) -> bool:
        """是否完成"""
        return self.status in [InferenceStatus.COMPLETED, InferenceStatus.FAILED, InferenceStatus.TIMEOUT]
    
    @property
    def is_successful(self) -> bool:
        """是否成功"""
        return self.status == InferenceStatus.COMPLETED
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """持续时间（秒）"""
        if self.total_time_ms is not None:
            return self.total_time_ms / 1000.0
        return None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": str(self.id),
            "request_id": self.request_id,
            "model_id": str(self.model_id),
            "deployment_id": str(self.deployment_id) if self.deployment_id else None,
            "user_id": self.user_id,
            "status": self.status.value,
            "model_version": self.model_version,
            "model_type": self.model_type,
            "device_used": self.device_used,
            "queue_time_ms": self.queue_time_ms,
            "processing_time_ms": self.processing_time_ms,
            "total_time_ms": self.total_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "gpu_memory_usage_mb": self.gpu_memory_usage_mb,
            "input_size_bytes": self.input_size_bytes,
            "output_size_bytes": self.output_size_bytes,
            "confidence_score": self.confidence_score,
            "quality_score": self.quality_score,
            "cache_hit": self.cache_hit,
            "error_message": self.error_message,
            "error_code": self.error_code,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }
    
    def to_summary_dict(self) -> Dict:
        """转换为摘要字典（不包含大数据字段）"""
        return {
            "id": str(self.id),
            "request_id": self.request_id,
            "model_id": str(self.model_id),
            "status": self.status.value,
            "model_type": self.model_type,
            "total_time_ms": self.total_time_ms,
            "cache_hit": self.cache_hit,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class ModelMetrics(Base):
    """模型指标表（聚合数据）"""
    __tablename__ = "model_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id"), nullable=False)
    
    # 时间维度
    date = Column(DateTime(timezone=True), nullable=False)  # 统计日期
    hour = Column(Integer, nullable=True)  # 小时（0-23），用于小时级统计
    
    # 请求统计
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    failed_requests = Column(Integer, default=0)
    timeout_requests = Column(Integer, default=0)
    cancelled_requests = Column(Integer, default=0)
    
    # 性能统计
    avg_processing_time_ms = Column(Float, nullable=True)
    min_processing_time_ms = Column(Float, nullable=True)
    max_processing_time_ms = Column(Float, nullable=True)
    p95_processing_time_ms = Column(Float, nullable=True)
    p99_processing_time_ms = Column(Float, nullable=True)
    
    # 资源使用统计
    avg_memory_usage_mb = Column(Float, nullable=True)
    max_memory_usage_mb = Column(Float, nullable=True)
    avg_gpu_memory_usage_mb = Column(Float, nullable=True)
    max_gpu_memory_usage_mb = Column(Float, nullable=True)
    
    # 数据量统计
    total_input_bytes = Column(Integer, default=0)
    total_output_bytes = Column(Integer, default=0)
    avg_input_size_bytes = Column(Float, nullable=True)
    avg_output_size_bytes = Column(Float, nullable=True)
    
    # 质量统计
    avg_confidence_score = Column(Float, nullable=True)
    avg_quality_score = Column(Float, nullable=True)
    
    # 缓存统计
    cache_hit_count = Column(Integer, default=0)
    cache_miss_count = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    model = relationship("AIModel")
    
    # 索引
    __table_args__ = (
        Index("idx_model_date", "model_id", "date"),
        Index("idx_model_date_hour", "model_id", "date", "hour"),
        Index("idx_date", "date"),
    )
    
    def __repr__(self):
        return f"<ModelMetrics(model_id={self.model_id}, date={self.date})>"
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_cache_requests = self.cache_hit_count + self.cache_miss_count
        if total_cache_requests == 0:
            return 0.0
        return self.cache_hit_count / total_cache_requests
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "model_id": str(self.model_id),
            "date": self.date.isoformat(),
            "hour": self.hour,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.success_rate,
            "avg_processing_time_ms": self.avg_processing_time_ms,
            "p95_processing_time_ms": self.p95_processing_time_ms,
            "p99_processing_time_ms": self.p99_processing_time_ms,
            "avg_memory_usage_mb": self.avg_memory_usage_mb,
            "max_memory_usage_mb": self.max_memory_usage_mb,
            "cache_hit_rate": self.cache_hit_rate,
            "avg_confidence_score": self.avg_confidence_score,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
