"""
日志配置模块
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.stdlib import LoggerFactory

from ..config import get_settings


def setup_logging() -> structlog.stdlib.BoundLogger:
    """设置结构化日志"""
    settings = get_settings()
    
    # 配置标准库日志
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level.upper()),
    )
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.environment == "production"
            else structlog.dev.Console<PERSON>enderer(colors=True),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 创建logger
    logger = structlog.get_logger("ai_model_service")
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("torch").setLevel(logging.WARNING)
    
    return logger


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self, logger: structlog.stdlib.BoundLogger):
        self.logger = logger
    
    def log_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        request_id: str,
        user_id: str = None,
        **kwargs: Any
    ) -> None:
        """记录请求日志"""
        log_data = {
            "event": "http_request",
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration_ms": round(duration * 1000, 2),
            "request_id": request_id,
        }
        
        if user_id:
            log_data["user_id"] = user_id
        
        log_data.update(kwargs)
        
        if status_code >= 500:
            self.logger.error("HTTP request failed", **log_data)
        elif status_code >= 400:
            self.logger.warning("HTTP request error", **log_data)
        else:
            self.logger.info("HTTP request", **log_data)
    
    def log_model_inference(
        self,
        model_id: str,
        model_type: str,
        duration: float,
        input_size: int,
        output_size: int,
        request_id: str,
        success: bool = True,
        error: str = None,
        **kwargs: Any
    ) -> None:
        """记录模型推理日志"""
        log_data = {
            "event": "model_inference",
            "model_id": model_id,
            "model_type": model_type,
            "duration_ms": round(duration * 1000, 2),
            "input_size": input_size,
            "output_size": output_size,
            "request_id": request_id,
            "success": success,
        }
        
        if error:
            log_data["error"] = error
        
        log_data.update(kwargs)
        
        if success:
            self.logger.info("Model inference completed", **log_data)
        else:
            self.logger.error("Model inference failed", **log_data)
    
    def log_model_operation(
        self,
        operation: str,
        model_id: str,
        duration: float = None,
        success: bool = True,
        error: str = None,
        **kwargs: Any
    ) -> None:
        """记录模型操作日志"""
        log_data = {
            "event": "model_operation",
            "operation": operation,
            "model_id": model_id,
            "success": success,
        }
        
        if duration is not None:
            log_data["duration_ms"] = round(duration * 1000, 2)
        
        if error:
            log_data["error"] = error
        
        log_data.update(kwargs)
        
        if success:
            self.logger.info(f"Model {operation} completed", **log_data)
        else:
            self.logger.error(f"Model {operation} failed", **log_data)


# 全局logger实例
logger = setup_logging()
request_logger = RequestLogger(logger)
