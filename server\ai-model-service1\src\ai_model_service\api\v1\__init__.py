"""
API v1 路由模块
"""

from fastapi import APIRouter

from .models import router as models_router
from .inference import router as inference_router
from .deployments import router as deployments_router
from .metrics import router as metrics_router

# 创建API路由器
api_router = APIRouter()

# 包含子路由
api_router.include_router(
    models_router,
    prefix="/models",
    tags=["models"]
)

api_router.include_router(
    inference_router,
    prefix="/inference",
    tags=["inference"]
)

api_router.include_router(
    deployments_router,
    prefix="/deployments",
    tags=["deployments"]
)

api_router.include_router(
    metrics_router,
    prefix="/metrics",
    tags=["metrics"]
)

# 根路径
@api_router.get("/")
async def root():
    """API根路径"""
    return {
        "message": "AI模型微服务 API v1",
        "version": "1.0.0",
        "endpoints": {
            "models": "/api/v1/models",
            "inference": "/api/v1/inference",
            "deployments": "/api/v1/deployments",
            "metrics": "/api/v1/metrics",
        }
    }
