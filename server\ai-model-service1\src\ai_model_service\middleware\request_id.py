"""
请求ID中间件
为每个请求生成唯一ID，用于链路追踪
"""

import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class RequestIDMiddleware(BaseHTTPMiddleware):
    """请求ID中间件"""
    
    def __init__(self, app, header_name: str = "X-Request-ID"):
        super().__init__(app)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 从请求头获取或生成请求ID
        request_id = request.headers.get(self.header_name)
        if not request_id:
            request_id = str(uuid.uuid4())
        
        # 将请求ID存储到请求状态中
        request.state.request_id = request_id
        
        # 调用下一个中间件或路由处理器
        response = await call_next(request)
        
        # 在响应头中添加请求ID
        response.headers[self.header_name] = request_id
        
        return response
