import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  Index,
} from 'typeorm';
import { Asset } from '../../assets/entities/asset.entity';

export enum TagType {
  GENERAL = 'general',
  STYLE = 'style',
  MATERIAL = 'material',
  FUNCTION = 'function',
  THEME = 'theme',
  TECHNICAL = 'technical',
}

@Entity('tags')
@Index(['name', 'type'])
export class Tag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  @Index()
  name: string;

  @Column({ length: 200, nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TagType,
    default: TagType.GENERAL,
  })
  @Index()
  type: TagType;

  @Column({ length: 7, nullable: true })
  color: string; // 十六进制颜色代码

  @Column({ name: 'usage_count', default: 0 })
  usageCount: number;

  @Column({ name: 'is_featured', default: false })
  isFeatured: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // 关联关系
  @ManyToMany(() => Asset, asset => asset.tags)
  assets: Asset[];

  // 元数据
  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  // 时间戳
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get displayName(): string {
    return `#${this.name}`;
  }

  get isPopular(): boolean {
    return this.usageCount > 100;
  }
}
