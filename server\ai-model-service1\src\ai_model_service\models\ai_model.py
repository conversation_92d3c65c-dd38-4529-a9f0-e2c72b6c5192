"""
AI模型数据模型
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Enum as SQLEnum, Float, Integer, 
    JSON, String, Text, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..core.database import Base


class ModelType(str, Enum):
    """模型类型枚举"""
    TEXT_GENERATION = "text_generation"
    IMAGE_GENERATION = "image_generation"
    IMAGE_CLASSIFICATION = "image_classification"
    OBJECT_DETECTION = "object_detection"
    SPEECH_RECOGNITION = "speech_recognition"
    TEXT_TO_SPEECH = "text_to_speech"
    TRANSLATION = "translation"
    SUMMARIZATION = "summarization"
    QUESTION_ANSWERING = "question_answering"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    SCENE_UNDERSTANDING = "scene_understanding"
    SCENE_GENERATION = "scene_generation"
    CUSTOM = "custom"


class ModelFormat(str, Enum):
    """模型格式枚举"""
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    ONNX = "onnx"
    TENSORRT = "tensorrt"
    HUGGINGFACE = "huggingface"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    """模型状态枚举"""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    READY = "ready"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"
    DEPRECATED = "deprecated"
    DELETED = "deleted"


class DeploymentStatus(str, Enum):
    """部署状态枚举"""
    PENDING = "pending"
    DEPLOYING = "deploying"
    DEPLOYED = "deployed"
    FAILED = "failed"
    STOPPED = "stopped"


class AIModel(Base):
    """AI模型表"""
    __tablename__ = "ai_models"
    
    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    display_name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    version = Column(String(50), nullable=False, default="1.0.0")
    
    # 模型信息
    model_type = Column(SQLEnum(ModelType), nullable=False, index=True)
    model_format = Column(SQLEnum(ModelFormat), nullable=False)
    status = Column(SQLEnum(ModelStatus), nullable=False, default=ModelStatus.UPLOADING, index=True)
    
    # 文件信息
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)  # 字节
    file_hash = Column(String(64), nullable=True)  # SHA-256
    config_path = Column(String(500), nullable=True)
    
    # 模型配置
    input_schema = Column(JSON, nullable=True)  # 输入数据结构
    output_schema = Column(JSON, nullable=True)  # 输出数据结构
    parameters = Column(JSON, nullable=True)  # 模型参数
    metadata = Column(JSON, nullable=True)  # 元数据
    
    # 性能信息
    model_size_mb = Column(Float, nullable=True)
    memory_usage_mb = Column(Float, nullable=True)
    gpu_memory_mb = Column(Float, nullable=True)
    inference_time_ms = Column(Float, nullable=True)  # 平均推理时间
    
    # 硬件要求
    requires_gpu = Column(Boolean, default=False)
    min_gpu_memory_mb = Column(Integer, nullable=True)
    min_ram_mb = Column(Integer, nullable=True)
    supported_devices = Column(JSON, nullable=True)  # ["cpu", "cuda", "mps"]
    
    # 使用统计
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    failed_requests = Column(Integer, default=0)
    avg_response_time_ms = Column(Float, nullable=True)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # 版本控制
    is_latest = Column(Boolean, default=True)
    parent_model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id"), nullable=True)
    
    # 权限和可见性
    is_public = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_by = Column(String(255), nullable=True)  # 用户ID
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    parent_model = relationship("AIModel", remote_side=[id], backref="child_models")
    deployments = relationship("ModelDeployment", back_populates="model", cascade="all, delete-orphan")
    inference_logs = relationship("InferenceLog", back_populates="model", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index("idx_model_type_status", "model_type", "status"),
        Index("idx_name_version", "name", "version"),
        Index("idx_created_by_created_at", "created_by", "created_at"),
        UniqueConstraint("name", "version", name="uq_model_name_version"),
    )
    
    def __repr__(self):
        return f"<AIModel(id={self.id}, name={self.name}, version={self.version})>"
    
    @property
    def full_name(self) -> str:
        """完整名称"""
        return f"{self.name}:{self.version}"
    
    @property
    def is_ready(self) -> bool:
        """是否就绪"""
        return self.status in [ModelStatus.READY, ModelStatus.LOADED]
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": str(self.id),
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "version": self.version,
            "model_type": self.model_type.value,
            "model_format": self.model_format.value,
            "status": self.status.value,
            "file_size": self.file_size,
            "model_size_mb": self.model_size_mb,
            "memory_usage_mb": self.memory_usage_mb,
            "requires_gpu": self.requires_gpu,
            "is_public": self.is_public,
            "is_active": self.is_active,
            "total_requests": self.total_requests,
            "success_rate": self.success_rate,
            "avg_response_time_ms": self.avg_response_time_ms,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
        }


class ModelDeployment(Base):
    """模型部署表"""
    __tablename__ = "model_deployments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id"), nullable=False)
    
    # 部署信息
    deployment_name = Column(String(255), nullable=False)
    status = Column(SQLEnum(DeploymentStatus), nullable=False, default=DeploymentStatus.PENDING)
    endpoint_url = Column(String(500), nullable=True)
    
    # 配置信息
    device = Column(String(50), nullable=False, default="cpu")  # cpu, cuda:0, cuda:1, etc.
    batch_size = Column(Integer, default=1)
    max_concurrent_requests = Column(Integer, default=10)
    timeout_seconds = Column(Integer, default=300)
    
    # 资源配置
    allocated_memory_mb = Column(Integer, nullable=True)
    allocated_gpu_memory_mb = Column(Integer, nullable=True)
    cpu_cores = Column(Float, nullable=True)
    
    # 负载均衡
    weight = Column(Float, default=1.0)  # 负载均衡权重
    priority = Column(Integer, default=0)  # 优先级
    
    # 健康检查
    health_check_url = Column(String(500), nullable=True)
    last_health_check = Column(DateTime(timezone=True), nullable=True)
    health_status = Column(String(50), default="unknown")
    
    # 统计信息
    request_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    avg_response_time_ms = Column(Float, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    deployed_at = Column(DateTime(timezone=True), nullable=True)
    stopped_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    model = relationship("AIModel", back_populates="deployments")
    
    # 索引
    __table_args__ = (
        Index("idx_model_status", "model_id", "status"),
        Index("idx_deployment_name", "deployment_name"),
        UniqueConstraint("deployment_name", name="uq_deployment_name"),
    )
    
    def __repr__(self):
        return f"<ModelDeployment(id={self.id}, name={self.deployment_name}, status={self.status})>"
    
    @property
    def is_healthy(self) -> bool:
        """是否健康"""
        return self.health_status == "healthy"
    
    @property
    def is_deployed(self) -> bool:
        """是否已部署"""
        return self.status == DeploymentStatus.DEPLOYED
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": str(self.id),
            "model_id": str(self.model_id),
            "deployment_name": self.deployment_name,
            "status": self.status.value,
            "endpoint_url": self.endpoint_url,
            "device": self.device,
            "batch_size": self.batch_size,
            "max_concurrent_requests": self.max_concurrent_requests,
            "weight": self.weight,
            "priority": self.priority,
            "health_status": self.health_status,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "avg_response_time_ms": self.avg_response_time_ms,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "deployed_at": self.deployed_at.isoformat() if self.deployed_at else None,
        }
