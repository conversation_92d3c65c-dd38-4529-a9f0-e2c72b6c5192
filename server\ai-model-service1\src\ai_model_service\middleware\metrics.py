"""
指标收集中间件
收集HTTP请求指标和性能数据
"""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST

from ..core.logger import request_logger
from ..config import get_settings


# Prometheus指标
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'http_requests_active',
    'Active HTTP requests'
)

MODEL_INFERENCE_COUNT = Counter(
    'model_inference_total',
    'Total model inferences',
    ['model_id', 'status']
)

MODEL_INFERENCE_DURATION = Histogram(
    'model_inference_duration_seconds',
    'Model inference duration in seconds',
    ['model_id']
)

LOADED_MODELS = Gauge(
    'loaded_models_count',
    'Number of loaded models'
)

GPU_MEMORY_USAGE = Gauge(
    'gpu_memory_usage_bytes',
    'GPU memory usage in bytes',
    ['device']
)

SYSTEM_MEMORY_USAGE = Gauge(
    'system_memory_usage_bytes',
    'System memory usage in bytes'
)


class MetricsMiddleware(BaseHTTPMiddleware):
    """指标收集中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.enabled = self.settings.metrics_enabled
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not self.enabled:
            return await call_next(request)
        
        # 处理Prometheus指标端点
        if request.url.path == "/metrics":
            return Response(
                generate_latest(),
                media_type=CONTENT_TYPE_LATEST
            )
        
        # 记录请求开始
        start_time = time.time()
        ACTIVE_REQUESTS.inc()
        
        # 获取请求信息
        method = request.method
        path = request.url.path
        
        # 简化路径（移除路径参数）
        endpoint = self._normalize_path(path)
        
        try:
            # 调用下一个中间件或路由处理器
            response = await call_next(request)
            status_code = response.status_code
            
        except Exception as e:
            # 处理异常情况
            status_code = 500
            response = Response(
                content={"error": "Internal Server Error"},
                status_code=500
            )
        
        finally:
            # 记录请求结束
            duration = time.time() - start_time
            ACTIVE_REQUESTS.dec()
            
            # 更新指标
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
            # 记录请求日志
            request_logger.log_request(
                method=method,
                path=path,
                status_code=status_code,
                duration=duration,
                request_id=getattr(request.state, 'request_id', ''),
                user_id=getattr(request.state, 'user_id', None)
            )
        
        return response
    
    def _normalize_path(self, path: str) -> str:
        """标准化路径，移除路径参数"""
        # 简单的路径标准化，实际项目中可能需要更复杂的逻辑
        parts = path.split('/')
        normalized_parts = []
        
        for part in parts:
            # 检查是否是UUID或数字ID
            if self._is_id_like(part):
                normalized_parts.append('{id}')
            else:
                normalized_parts.append(part)
        
        return '/'.join(normalized_parts)
    
    def _is_id_like(self, part: str) -> bool:
        """检查是否像ID"""
        if not part:
            return False
        
        # 检查是否是UUID格式
        if len(part) == 36 and part.count('-') == 4:
            return True
        
        # 检查是否是纯数字
        if part.isdigit():
            return True
        
        return False


class ModelMetricsCollector:
    """模型指标收集器"""
    
    @staticmethod
    def record_inference(model_id: str, duration: float, success: bool):
        """记录推理指标"""
        status = "success" if success else "error"
        
        MODEL_INFERENCE_COUNT.labels(
            model_id=model_id,
            status=status
        ).inc()
        
        if success:
            MODEL_INFERENCE_DURATION.labels(
                model_id=model_id
            ).observe(duration)
    
    @staticmethod
    def update_loaded_models_count(count: int):
        """更新已加载模型数量"""
        LOADED_MODELS.set(count)
    
    @staticmethod
    def update_gpu_memory_usage(device: str, usage_bytes: int):
        """更新GPU内存使用量"""
        GPU_MEMORY_USAGE.labels(device=device).set(usage_bytes)
    
    @staticmethod
    def update_system_memory_usage(usage_bytes: int):
        """更新系统内存使用量"""
        SYSTEM_MEMORY_USAGE.set(usage_bytes)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.request_times = []
        self.max_samples = 1000
    
    def record_request_time(self, duration: float):
        """记录请求时间"""
        self.request_times.append(duration)
        
        # 保持样本数量在限制内
        if len(self.request_times) > self.max_samples:
            self.request_times = self.request_times[-self.max_samples:]
    
    def get_percentiles(self) -> dict:
        """获取百分位数"""
        if not self.request_times:
            return {}
        
        sorted_times = sorted(self.request_times)
        length = len(sorted_times)
        
        return {
            'p50': sorted_times[int(length * 0.5)],
            'p90': sorted_times[int(length * 0.9)],
            'p95': sorted_times[int(length * 0.95)],
            'p99': sorted_times[int(length * 0.99)],
        }
    
    def get_average(self) -> float:
        """获取平均响应时间"""
        if not self.request_times:
            return 0.0
        
        return sum(self.request_times) / len(self.request_times)


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
