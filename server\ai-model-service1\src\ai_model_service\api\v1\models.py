"""
模型管理API路由
"""

import uuid
from typing import List, Optional, Dict, Any
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Request
from fastapi.responses import JSONResponse
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db_session
from ...core.storage import storage_manager
from ...models.ai_model import AIModel, ModelType, ModelFormat, ModelStatus
from ...schemas.model_schemas import (
    ModelCreate, ModelUpdate, ModelResponse, ModelListResponse,
    ModelUploadResponse, ModelStatusUpdate
)
from ...core.logger import logger, request_logger
from ...config import get_settings

router = APIRouter()
settings = get_settings()


@router.post("/", response_model=ModelResponse)
async def create_model(
    model_data: ModelCreate,
    request: Request,
    db: AsyncSession = Depends(get_db_session)
):
    """创建新模型"""
    try:
        # 检查模型名称和版本是否已存在
        result = await db.execute(
            select(AIModel).where(
                AIModel.name == model_data.name,
                AIModel.version == model_data.version,
                AIModel.deleted_at.is_(None)
            )
        )
        existing_model = result.scalar_one_or_none()
        
        if existing_model:
            raise HTTPException(
                status_code=400,
                detail=f"模型 {model_data.name}:{model_data.version} 已存在"
            )
        
        # 创建模型记录
        model = AIModel(
            name=model_data.name,
            display_name=model_data.display_name,
            description=model_data.description,
            version=model_data.version,
            model_type=model_data.model_type,
            model_format=model_data.model_format,
            input_schema=model_data.input_schema,
            output_schema=model_data.output_schema,
            parameters=model_data.parameters,
            metadata=model_data.metadata,
            requires_gpu=model_data.requires_gpu,
            min_gpu_memory_mb=model_data.min_gpu_memory_mb,
            min_ram_mb=model_data.min_ram_mb,
            supported_devices=model_data.supported_devices,
            is_public=model_data.is_public,
            created_by=getattr(request.state, 'user_id', None),
            status=ModelStatus.UPLOADING
        )
        
        db.add(model)
        await db.commit()
        await db.refresh(model)
        
        logger.info(f"模型创建成功: {model.id}")
        
        return ModelResponse.from_orm(model)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建模型失败: {e}")
        raise HTTPException(status_code=500, detail="创建模型失败")


@router.get("/", response_model=ModelListResponse)
async def list_models(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    model_type: Optional[ModelType] = None,
    status: Optional[ModelStatus] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db_session)
):
    """获取模型列表"""
    try:
        # 构建查询
        query = select(AIModel).where(AIModel.deleted_at.is_(None))
        
        if model_type:
            query = query.where(AIModel.model_type == model_type)
        
        if status:
            query = query.where(AIModel.status == status)
        
        if search:
            query = query.where(
                AIModel.name.ilike(f"%{search}%") |
                AIModel.description.ilike(f"%{search}%")
            )
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(AIModel.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        models = result.scalars().all()
        
        return ModelListResponse(
            models=[ModelResponse.from_orm(model) for model in models],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取模型列表失败")


@router.get("/{model_id}", response_model=ModelResponse)
async def get_model(
    model_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """获取模型详情"""
    try:
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        return ModelResponse.from_orm(model)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取模型详情失败")


@router.put("/{model_id}", response_model=ModelResponse)
async def update_model(
    model_id: str,
    model_data: ModelUpdate,
    request: Request,
    db: AsyncSession = Depends(get_db_session)
):
    """更新模型信息"""
    try:
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 检查权限（简化版本，实际应该有更复杂的权限控制）
        user_id = getattr(request.state, 'user_id', None)
        if model.created_by != user_id:
            raise HTTPException(status_code=403, detail="无权限修改此模型")
        
        # 更新字段
        update_data = model_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(model, field, value)
        
        await db.commit()
        await db.refresh(model)
        
        logger.info(f"模型更新成功: {model_id}")
        
        return ModelResponse.from_orm(model)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新模型失败: {e}")
        raise HTTPException(status_code=500, detail="更新模型失败")


@router.delete("/{model_id}")
async def delete_model(
    model_id: str,
    request: Request,
    db: AsyncSession = Depends(get_db_session)
):
    """删除模型（软删除）"""
    try:
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 检查权限
        user_id = getattr(request.state, 'user_id', None)
        if model.created_by != user_id:
            raise HTTPException(status_code=403, detail="无权限删除此模型")
        
        # 软删除
        from datetime import datetime
        model.deleted_at = datetime.utcnow()
        model.status = ModelStatus.DELETED
        
        await db.commit()
        
        logger.info(f"模型删除成功: {model_id}")
        
        return {"message": "模型删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除模型失败: {e}")
        raise HTTPException(status_code=500, detail="删除模型失败")


@router.post("/{model_id}/upload", response_model=ModelUploadResponse)
async def upload_model_file(
    model_id: str,
    file: UploadFile = File(...),
    file_type: str = Form("model"),  # model, config, tokenizer
    request: Request = None,
    db: AsyncSession = Depends(get_db_session)
):
    """上传模型文件"""
    try:
        # 检查模型是否存在
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 检查文件大小
        if file.size > settings.max_file_size_mb * 1024 * 1024:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 ({settings.max_file_size_mb}MB)"
            )
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.allowed_file_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式: {file_ext}"
            )
        
        # 生成对象名称
        object_name = f"{model.name}/{model.version}/{file_type}/{file.filename}"
        
        # 保存临时文件
        temp_file = Path(f"/tmp/{uuid.uuid4()}_{file.filename}")
        try:
            with open(temp_file, "wb") as f:
                content = await file.read()
                f.write(content)
            
            # 上传到存储
            upload_result = await storage_manager.upload_file(
                bucket=settings.minio_bucket_models,
                object_name=object_name,
                file_path=temp_file,
                content_type=file.content_type,
                metadata={
                    'model-id': model_id,
                    'file-type': file_type,
                    'uploaded-by': getattr(request.state, 'user_id', 'unknown'),
                }
            )
            
            # 更新模型记录
            if file_type == "model":
                model.file_path = object_name
                model.file_size = upload_result['file_size']
                model.file_hash = upload_result['file_hash']
                model.model_size_mb = upload_result['file_size'] / 1024 / 1024
                model.status = ModelStatus.PROCESSING
            elif file_type == "config":
                model.config_path = object_name
            
            await db.commit()
            
            logger.info(f"模型文件上传成功: {model_id}/{file_type}")
            
            return ModelUploadResponse(
                model_id=model_id,
                file_type=file_type,
                object_name=object_name,
                file_size=upload_result['file_size'],
                file_hash=upload_result['file_hash'],
                message="文件上传成功"
            )
            
        finally:
            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传模型文件失败: {e}")
        raise HTTPException(status_code=500, detail="上传模型文件失败")


@router.patch("/{model_id}/status", response_model=ModelResponse)
async def update_model_status(
    model_id: str,
    status_data: ModelStatusUpdate,
    db: AsyncSession = Depends(get_db_session)
):
    """更新模型状态"""
    try:
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 更新状态
        model.status = status_data.status
        if status_data.error_message:
            if not model.metadata:
                model.metadata = {}
            model.metadata['error_message'] = status_data.error_message
        
        await db.commit()
        await db.refresh(model)
        
        logger.info(f"模型状态更新成功: {model_id} -> {status_data.status}")
        
        return ModelResponse.from_orm(model)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新模型状态失败: {e}")
        raise HTTPException(status_code=500, detail="更新模型状态失败")


@router.get("/{model_id}/download")
async def download_model(
    model_id: str,
    file_type: str = Query("model", description="文件类型"),
    db: AsyncSession = Depends(get_db_session)
):
    """生成模型下载链接"""
    try:
        result = await db.execute(
            select(AIModel).where(
                AIModel.id == model_id,
                AIModel.deleted_at.is_(None)
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        # 获取文件路径
        if file_type == "model":
            object_name = model.file_path
        elif file_type == "config":
            object_name = model.config_path
        else:
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        if not object_name:
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 生成预签名URL
        download_url = await storage_manager.generate_presigned_url(
            bucket=settings.minio_bucket_models,
            object_name=object_name,
            expires=3600,  # 1小时有效期
            method="GET"
        )
        
        return {"download_url": download_url}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成下载链接失败: {e}")
        raise HTTPException(status_code=500, detail="生成下载链接失败")
