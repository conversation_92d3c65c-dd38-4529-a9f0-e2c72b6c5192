# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# 缓存
redis==5.0.1
hiredis==2.2.3

# AI/ML框架
torch==2.1.1
torchvision==0.16.1
transformers==4.36.0
diffusers==0.24.0
accelerate==0.25.0
safetensors==0.4.1

# 图像处理
Pillow==10.1.0
opencv-python==********
numpy==1.24.4

# 音频处理
librosa==0.10.1
soundfile==0.12.1
whisper==1.1.10

# 3D处理
trimesh==4.0.5
open3d==0.18.0

# 文件存储
minio==7.2.0
boto3==1.34.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 工具库
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
click==8.1.7
typer==0.9.0

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0
colorama==0.4.6

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 类型提示
types-redis==********
types-requests==*********

# 异步任务
celery==5.3.4
flower==2.0.1

# 模型优化
onnx==1.15.0
onnxruntime==1.16.3
tensorrt==8.6.1

# GPU支持 (可选)
# torch-audio==2.1.1+cu118
# torchaudio==2.1.1+cu118
