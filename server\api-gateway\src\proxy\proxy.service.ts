import { Injectable, Logger, BadGatewayException, RequestTimeoutException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Request, Response } from 'express';
import { GatewayService } from '../gateway/gateway.service';
import { LoadBalancerService } from '../gateway/load-balancer.service';

export interface ProxyOptions {
  timeout?: number;
  retries?: number;
  stripPath?: boolean;
  preserveHost?: boolean;
  followRedirects?: boolean;
}

export interface ProxyStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  requestsByService: Record<string, number>;
  errorsByService: Record<string, number>;
}

@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name);
  private readonly httpClient: AxiosInstance;
  private readonly stats: ProxyStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    requestsByService: {},
    errorsByService: {},
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly gatewayService: GatewayService,
    private readonly loadBalancer: LoadBalancerService,
  ) {
    this.httpClient = axios.create({
      timeout: this.configService.get('PROXY_DEFAULT_TIMEOUT', 30000),
      maxRedirects: this.configService.get('PROXY_MAX_REDIRECTS', 5),
      validateStatus: () => true, // 不要自动抛出错误，让我们处理所有状态码
    });

    this.setupInterceptors();
  }

  /**
   * 代理请求到后端服务
   */
  async proxyRequest(
    req: Request,
    res: Response,
    options: ProxyOptions = {}
  ): Promise<void> {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] as string || this.generateRequestId();
    
    try {
      // 获取路由配置
      const route = this.gatewayService.getRoute(req.path);
      if (!route) {
        res.status(404).json({
          error: 'Route not found',
          path: req.path,
          requestId,
        });
        return;
      }

      // 获取服务实例
      const instances = await this.gatewayService.getServiceInstance(route.serviceName);
      if (!instances) {
        res.status(503).json({
          error: 'Service unavailable',
          service: route.serviceName,
          requestId,
        });
        return;
      }

      // 构建目标URL
      const targetUrl = this.buildTargetUrl(instances, req, route, options);
      
      // 准备请求配置
      const proxyConfig = this.buildProxyConfig(req, route, options, requestId);

      // 增加连接计数
      this.loadBalancer.incrementConnections(instances);

      try {
        // 发送代理请求
        const response = await this.sendProxyRequest(targetUrl, proxyConfig);
        
        // 处理响应
        await this.handleProxyResponse(response, res, requestId);
        
        // 更新统计信息
        const responseTime = Date.now() - startTime;
        this.updateStats(route.serviceName, true, responseTime);
        this.loadBalancer.updateStats(instances, responseTime, false);

      } finally {
        // 减少连接计数
        this.loadBalancer.decrementConnections(instances);
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      await this.handleProxyError(error, res, requestId, responseTime);
    }
  }

  /**
   * 获取代理统计信息
   */
  getProxyStats(): ProxyStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats.totalRequests = 0;
    this.stats.successfulRequests = 0;
    this.stats.failedRequests = 0;
    this.stats.averageResponseTime = 0;
    this.stats.requestsByService = {};
    this.stats.errorsByService = {};
    
    this.logger.log('代理统计信息已重置');
  }

  /**
   * 构建目标URL
   */
  private buildTargetUrl(
    instanceUrl: string,
    req: Request,
    route: any,
    options: ProxyOptions
  ): string {
    let targetPath = req.path;
    
    // 如果需要剥离路径前缀
    if (options.stripPath || route.stripPath) {
      targetPath = targetPath.replace(route.path, '');
      if (!targetPath.startsWith('/')) {
        targetPath = '/' + targetPath;
      }
    }

    // 添加查询参数
    const queryString = req.url.includes('?') ? req.url.split('?')[1] : '';
    const fullPath = queryString ? `${targetPath}?${queryString}` : targetPath;

    return `${instanceUrl}${fullPath}`;
  }

  /**
   * 构建代理请求配置
   */
  private buildProxyConfig(
    req: Request,
    route: any,
    options: ProxyOptions,
    requestId: string
  ): AxiosRequestConfig {
    const headers = { ...req.headers };
    
    // 移除可能导致问题的头部
    delete headers.host;
    delete headers['content-length'];
    delete headers.connection;
    
    // 添加代理头部
    headers['x-forwarded-for'] = req.ip;
    headers['x-forwarded-proto'] = req.protocol;
    headers['x-forwarded-host'] = req.get('host');
    headers['x-request-id'] = requestId;
    headers['x-gateway-service'] = route.serviceName;

    // 如果需要保留原始host
    if (options.preserveHost) {
      headers.host = req.get('host');
    }

    return {
      method: req.method as any,
      headers,
      data: req.body,
      timeout: options.timeout || route.timeout || 30000,
      maxRedirects: options.followRedirects ? 5 : 0,
      validateStatus: () => true,
    };
  }

  /**
   * 发送代理请求
   */
  private async sendProxyRequest(
    targetUrl: string,
    config: AxiosRequestConfig
  ): Promise<AxiosResponse> {
    this.logger.debug(`代理请求: ${config.method?.toUpperCase()} ${targetUrl}`);
    
    try {
      return await this.httpClient.request({
        ...config,
        url: targetUrl,
      });
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new RequestTimeoutException('请求超时');
      }
      throw new BadGatewayException(`代理请求失败: ${error.message}`);
    }
  }

  /**
   * 处理代理响应
   */
  private async handleProxyResponse(
    response: AxiosResponse,
    res: Response,
    requestId: string
  ): Promise<void> {
    // 设置响应头
    const responseHeaders = { ...response.headers };
    
    // 移除可能导致问题的头部
    delete responseHeaders['transfer-encoding'];
    delete responseHeaders.connection;
    
    // 添加代理头部
    responseHeaders['x-request-id'] = requestId;
    responseHeaders['x-proxied-by'] = 'api-gateway';

    // 设置状态码和头部
    res.status(response.status);
    Object.entries(responseHeaders).forEach(([key, value]) => {
      if (value !== undefined) {
        res.set(key, value as string);
      }
    });

    // 发送响应体
    if (response.data) {
      res.send(response.data);
    } else {
      res.end();
    }

    this.logger.debug(`代理响应: ${response.status} ${requestId}`);
  }

  /**
   * 处理代理错误
   */
  private async handleProxyError(
    error: any,
    res: Response,
    requestId: string,
    responseTime: number
  ): Promise<void> {
    this.logger.error(`代理错误: ${requestId}`, error);

    // 更新错误统计
    this.updateStats('unknown', false, responseTime);

    let statusCode = 500;
    let errorMessage = '内部服务器错误';

    if (error instanceof RequestTimeoutException) {
      statusCode = 408;
      errorMessage = '请求超时';
    } else if (error instanceof BadGatewayException) {
      statusCode = 502;
      errorMessage = '网关错误';
    } else if (error.code === 'ECONNREFUSED') {
      statusCode = 503;
      errorMessage = '服务不可用';
    } else if (error.code === 'ENOTFOUND') {
      statusCode = 502;
      errorMessage = '服务地址无法解析';
    }

    res.status(statusCode).json({
      error: errorMessage,
      requestId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 更新统计信息
   */
  private updateStats(serviceName: string, success: boolean, responseTime: number): void {
    this.stats.totalRequests++;
    
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
      this.stats.errorsByService[serviceName] = (this.stats.errorsByService[serviceName] || 0) + 1;
    }

    this.stats.requestsByService[serviceName] = (this.stats.requestsByService[serviceName] || 0) + 1;
    
    // 计算移动平均响应时间
    this.stats.averageResponseTime = (this.stats.averageResponseTime * 0.9) + (responseTime * 0.1);
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        return config;
      },
      (error) => {
        this.logger.error('代理请求拦截器错误', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        const duration = Date.now() - response.config.metadata?.startTime;
        this.logger.debug(`代理响应: ${response.status} ${response.config.url} (${duration}ms)`);
        return response;
      },
      (error) => {
        const duration = Date.now() - error.config?.metadata?.startTime;
        this.logger.error(`代理请求失败: ${error.config?.url} (${duration}ms)`, error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
