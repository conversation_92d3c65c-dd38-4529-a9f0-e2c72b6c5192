import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface ElasticsearchConfig {
  node: string;
  auth?: {
    username: string;
    password: string;
  };
  maxRetries: number;
  requestTimeout: number;
  pingTimeout: number;
  sniffOnStart: boolean;
  sniffInterval: number;
  sniffOnConnectionFault: boolean;
  resurrectStrategy: string;
  log: string;
}

@Injectable()
export class ElasticsearchConfig {
  constructor(private configService: ConfigService) {}

  createElasticsearchOptions(): ElasticsearchConfig {
    const config: ElasticsearchConfig = {
      node: this.configService.get('ELASTICSEARCH_NODE', 'http://localhost:9200'),
      maxRetries: this.configService.get('ELASTICSEARCH_MAX_RETRIES', 3),
      requestTimeout: this.configService.get('ELASTICSEARCH_REQUEST_TIMEOUT', 60000),
      pingTimeout: this.configService.get('ELASTICSEARCH_PING_TIMEOUT', 3000),
      sniffOnStart: this.configService.get('ELASTICSEARCH_SNIFF_ON_START', false),
      sniffInterval: this.configService.get('ELASTICSEARCH_SNIFF_INTERVAL', 300000),
      sniffOnConnectionFault: this.configService.get('ELASTICSEARCH_SNIFF_ON_CONNECTION_FAULT', false),
      resurrectStrategy: this.configService.get('ELASTICSEARCH_RESURRECT_STRATEGY', 'ping'),
      log: this.configService.get('ELASTICSEARCH_LOG_LEVEL', 'error'),
    };

    // 如果配置了用户名和密码，添加认证信息
    const username = this.configService.get('ELASTICSEARCH_USERNAME');
    const password = this.configService.get('ELASTICSEARCH_PASSWORD');
    
    if (username && password) {
      config.auth = { username, password };
    }

    return config;
  }

  getIndexSettings() {
    return {
      assets: {
        index: this.configService.get('ELASTICSEARCH_ASSETS_INDEX', 'assets'),
        settings: {
          number_of_shards: this.configService.get('ELASTICSEARCH_ASSETS_SHARDS', 1),
          number_of_replicas: this.configService.get('ELASTICSEARCH_ASSETS_REPLICAS', 0),
          analysis: {
            analyzer: {
              asset_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'stop', 'snowball'],
              },
              autocomplete_analyzer: {
                type: 'custom',
                tokenizer: 'autocomplete_tokenizer',
                filter: ['lowercase'],
              },
            },
            tokenizer: {
              autocomplete_tokenizer: {
                type: 'edge_ngram',
                min_gram: 2,
                max_gram: 20,
                token_chars: ['letter', 'digit'],
              },
            },
          },
        },
        mappings: {
          properties: {
            id: { type: 'keyword' },
            name: {
              type: 'text',
              analyzer: 'asset_analyzer',
              fields: {
                keyword: { type: 'keyword' },
                autocomplete: {
                  type: 'text',
                  analyzer: 'autocomplete_analyzer',
                },
              },
            },
            description: {
              type: 'text',
              analyzer: 'asset_analyzer',
            },
            type: { type: 'keyword' },
            status: { type: 'keyword' },
            license: { type: 'keyword' },
            category: {
              type: 'object',
              properties: {
                id: { type: 'keyword' },
                name: {
                  type: 'text',
                  analyzer: 'asset_analyzer',
                  fields: {
                    keyword: { type: 'keyword' },
                  },
                },
              },
            },
            tags: {
              type: 'nested',
              properties: {
                id: { type: 'keyword' },
                name: {
                  type: 'text',
                  analyzer: 'asset_analyzer',
                  fields: {
                    keyword: { type: 'keyword' },
                  },
                },
                type: { type: 'keyword' },
              },
            },
            creator: {
              type: 'object',
              properties: {
                id: { type: 'keyword' },
                username: { type: 'keyword' },
                displayName: {
                  type: 'text',
                  analyzer: 'asset_analyzer',
                },
              },
            },
            metadata: {
              type: 'object',
              dynamic: true,
            },
            downloadCount: { type: 'integer' },
            viewCount: { type: 'integer' },
            likeCount: { type: 'integer' },
            rating: { type: 'float' },
            ratingCount: { type: 'integer' },
            fileSize: { type: 'long' },
            fileFormat: { type: 'keyword' },
            createdAt: { type: 'date' },
            updatedAt: { type: 'date' },
            publishedAt: { type: 'date' },
          },
        },
      },
    };
  }
}
