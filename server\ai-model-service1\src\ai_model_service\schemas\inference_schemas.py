"""
推理相关的数据模式
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from uuid import UUID

from pydantic import BaseModel, Field

from ..models.inference_log import InferenceStatus


class InferenceRequest(BaseModel):
    """推理请求"""
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    parameters: Optional[Dict[str, Any]] = Field(None, description="推理参数")
    
    class Config:
        schema_extra = {
            "example": {
                "input_data": {
                    "text": "Hello, how are you?",
                    "max_length": 100,
                    "temperature": 0.7
                },
                "parameters": {
                    "do_sample": True,
                    "top_p": 0.9
                }
            }
        }


class InferenceResponse(BaseModel):
    """推理响应"""
    request_id: str = Field(..., description="请求ID")
    model_id: str = Field(..., description="模型ID")
    output_data: Dict[str, Any] = Field(..., description="输出数据")
    metadata: Dict[str, Any] = Field(..., description="元数据")
    success: bool = Field(..., description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        schema_extra = {
            "example": {
                "request_id": "123e4567-e89b-12d3-a456-426614174000",
                "model_id": "456e7890-e89b-12d3-a456-426614174000",
                "output_data": {
                    "generated_text": "Hello! I'm doing well, thank you for asking.",
                    "confidence": 0.95
                },
                "metadata": {
                    "processing_time_ms": 150.5,
                    "device": "cuda:0",
                    "model_version": "1.0.0"
                },
                "success": True
            }
        }


class BatchInferenceRequest(BaseModel):
    """批量推理请求"""
    inputs: List[Dict[str, Any]] = Field(..., description="输入数据列表")
    parameters: Optional[Dict[str, Any]] = Field(None, description="推理参数")
    
    class Config:
        schema_extra = {
            "example": {
                "inputs": [
                    {"text": "Hello, how are you?"},
                    {"text": "What's the weather like?"},
                    {"text": "Tell me a joke."}
                ],
                "parameters": {
                    "max_length": 100,
                    "temperature": 0.7
                }
            }
        }


class BatchInferenceResponse(BaseModel):
    """批量推理响应"""
    batch_id: str = Field(..., description="批次ID")
    model_id: str = Field(..., description="模型ID")
    results: List[Dict[str, Any]] = Field(..., description="结果列表")
    total_count: int = Field(..., description="总数量")
    successful_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    processing_time_ms: float = Field(..., description="处理时间(毫秒)")
    
    class Config:
        schema_extra = {
            "example": {
                "batch_id": "batch_123e4567-e89b-12d3-a456-426614174000",
                "model_id": "456e7890-e89b-12d3-a456-426614174000",
                "results": [
                    {
                        "index": 0,
                        "success": True,
                        "output_data": {"generated_text": "Hello! I'm doing well."},
                        "metadata": {"processing_time_ms": 120.5}
                    },
                    {
                        "index": 1,
                        "success": True,
                        "output_data": {"generated_text": "It's sunny today."},
                        "metadata": {"processing_time_ms": 135.2}
                    }
                ],
                "total_count": 2,
                "successful_count": 2,
                "failed_count": 0,
                "processing_time_ms": 255.7
            }
        }


class InferenceLogResponse(BaseModel):
    """推理日志响应"""
    id: UUID
    request_id: str
    model_id: UUID
    deployment_id: Optional[UUID] = None
    user_id: Optional[str] = None
    client_ip: Optional[str] = None
    
    status: InferenceStatus
    model_version: Optional[str] = None
    model_type: Optional[str] = None
    device_used: Optional[str] = None
    
    # 性能指标
    queue_time_ms: Optional[float] = None
    processing_time_ms: Optional[float] = None
    total_time_ms: Optional[float] = None
    
    # 资源使用
    memory_usage_mb: Optional[float] = None
    gpu_memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    
    # 数据大小
    input_size_bytes: Optional[int] = None
    output_size_bytes: Optional[int] = None
    
    # 质量指标
    confidence_score: Optional[float] = None
    quality_score: Optional[float] = None
    
    # 缓存信息
    cache_hit: bool = False
    cache_key: Optional[str] = None
    
    # 错误信息
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    
    # 时间戳
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class InferenceListResponse(BaseModel):
    """推理日志列表响应"""
    logs: List[InferenceLogResponse]
    total: int
    skip: int
    limit: int


class InferenceStatsResponse(BaseModel):
    """推理统计响应"""
    model_id: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    success_rate: float
    avg_processing_time_ms: float
    avg_queue_time_ms: float
    avg_memory_usage_mb: float
    cache_hit_rate: float
    
    # 时间范围统计
    stats_period: str  # "1h", "24h", "7d", "30d"
    start_time: datetime
    end_time: datetime


class ModelPerformanceResponse(BaseModel):
    """模型性能响应"""
    model_id: str
    model_name: str
    model_version: str
    
    # 当前状态
    is_loaded: bool
    device: Optional[str] = None
    memory_usage_mb: Optional[float] = None
    
    # 性能指标
    avg_response_time_ms: float
    requests_per_minute: float
    success_rate: float
    
    # 资源使用
    cpu_usage_percent: float
    memory_usage_percent: float
    gpu_usage_percent: Optional[float] = None
    
    # 队列状态
    queue_size: int
    max_queue_size: int
    
    # 最近活动
    last_request_time: Optional[datetime] = None
    uptime_seconds: Optional[float] = None


class SystemMetricsResponse(BaseModel):
    """系统指标响应"""
    timestamp: datetime
    
    # 模型统计
    total_models: int
    loaded_models: int
    active_models: int
    
    # 请求统计
    total_requests_1h: int
    successful_requests_1h: int
    failed_requests_1h: int
    avg_response_time_1h: float
    
    # 系统资源
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_usage_percent: float
    
    # GPU资源（如果可用）
    gpu_count: int
    gpu_usage_percent: Optional[List[float]] = None
    gpu_memory_usage_percent: Optional[List[float]] = None
    
    # 队列状态
    total_queue_size: int
    max_queue_size: int
    
    # 缓存统计
    cache_hit_rate: float
    cache_size_mb: float


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime
    version: str
    uptime_seconds: float
    
    # 服务状态
    services: Dict[str, str] = Field(..., description="各服务状态")
    
    # 资源状态
    memory_usage_percent: float
    disk_usage_percent: float
    
    # 模型状态
    loaded_models_count: int
    failed_models_count: int
    
    # 详细信息
    details: Optional[Dict[str, Any]] = None
