"""
配置管理模块
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8002, env="PORT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # 数据库配置
    database_url: str = Field(
        default="postgresql://postgres:password@localhost:5432/ai_models",
        env="DATABASE_URL"
    )
    database_pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379/2", env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=2, env="REDIS_DB")
    redis_max_connections: int = Field(default=50, env="REDIS_MAX_CONNECTIONS")
    
    # MinIO配置
    minio_endpoint: str = Field(default="localhost:9000", env="MINIO_ENDPOINT")
    minio_access_key: str = Field(default="minioadmin", env="MINIO_ACCESS_KEY")
    minio_secret_key: str = Field(default="minioadmin", env="MINIO_SECRET_KEY")
    minio_secure: bool = Field(default=False, env="MINIO_SECURE")
    minio_bucket_models: str = Field(default="ai-models", env="MINIO_BUCKET_MODELS")
    minio_bucket_cache: str = Field(default="model-cache", env="MINIO_BUCKET_CACHE")
    
    # JWT配置
    jwt_secret: str = Field(default="ai-model-secret", env="JWT_SECRET")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_expire_hours: int = Field(default=24, env="JWT_EXPIRE_HOURS")
    
    # CORS配置
    cors_origins: List[str] = Field(
        default=["*"],
        env="CORS_ORIGINS"
    )
    
    # 模型配置
    model_cache_dir: str = Field(default="./models", env="MODEL_CACHE_DIR")
    model_max_memory_gb: float = Field(default=8.0, env="MODEL_MAX_MEMORY_GB")
    model_max_concurrent: int = Field(default=4, env="MODEL_MAX_CONCURRENT")
    model_timeout_seconds: int = Field(default=300, env="MODEL_TIMEOUT_SECONDS")
    model_warmup_enabled: bool = Field(default=True, env="MODEL_WARMUP_ENABLED")
    
    # GPU配置
    gpu_enabled: bool = Field(default=True, env="GPU_ENABLED")
    gpu_device_ids: List[int] = Field(default=[0], env="GPU_DEVICE_IDS")
    gpu_memory_fraction: float = Field(default=0.8, env="GPU_MEMORY_FRACTION")
    
    # 推理配置
    inference_batch_size: int = Field(default=1, env="INFERENCE_BATCH_SIZE")
    inference_max_queue_size: int = Field(default=100, env="INFERENCE_MAX_QUEUE_SIZE")
    inference_worker_count: int = Field(default=2, env="INFERENCE_WORKER_COUNT")
    
    # 监控配置
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    metrics_port: int = Field(default=9092, env="METRICS_PORT")
    
    # 限流配置
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_requests_per_minute: int = Field(default=60, env="RATE_LIMIT_RPM")
    rate_limit_burst_size: int = Field(default=10, env="RATE_LIMIT_BURST")
    
    # 缓存配置
    cache_ttl_models: int = Field(default=3600, env="CACHE_TTL_MODELS")  # 1小时
    cache_ttl_predictions: int = Field(default=300, env="CACHE_TTL_PREDICTIONS")  # 5分钟
    cache_ttl_health: int = Field(default=60, env="CACHE_TTL_HEALTH")  # 1分钟
    
    # 模型类型配置
    supported_model_types: List[str] = Field(
        default=[
            "text_generation",
            "image_generation", 
            "image_classification",
            "object_detection",
            "speech_recognition",
            "text_to_speech",
            "translation",
            "summarization",
            "question_answering",
            "sentiment_analysis",
            "3d_generation",
            "scene_understanding"
        ],
        env="SUPPORTED_MODEL_TYPES"
    )
    
    # 模型格式配置
    supported_model_formats: List[str] = Field(
        default=[
            "pytorch",
            "tensorflow",
            "onnx",
            "tensorrt",
            "huggingface",
            "custom"
        ],
        env="SUPPORTED_MODEL_FORMATS"
    )
    
    # 安全配置
    max_file_size_mb: int = Field(default=1024, env="MAX_FILE_SIZE_MB")  # 1GB
    allowed_file_extensions: List[str] = Field(
        default=[".pt", ".pth", ".onnx", ".pb", ".h5", ".bin", ".safetensors"],
        env="ALLOWED_FILE_EXTENSIONS"
    )
    
    # 服务发现配置
    service_name: str = Field(default="ai-model-service", env="SERVICE_NAME")
    service_version: str = Field(default="1.0.0", env="SERVICE_VERSION")
    consul_host: str = Field(default="localhost", env="CONSUL_HOST")
    consul_port: int = Field(default=8500, env="CONSUL_PORT")
    consul_enabled: bool = Field(default=False, env="CONSUL_ENABLED")
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("gpu_device_ids", pre=True)
    def parse_gpu_device_ids(cls, v):
        if isinstance(v, str):
            return [int(device_id.strip()) for device_id in v.split(",")]
        return v
    
    @validator("supported_model_types", pre=True)
    def parse_supported_model_types(cls, v):
        if isinstance(v, str):
            return [model_type.strip() for model_type in v.split(",")]
        return v
    
    @validator("supported_model_formats", pre=True)
    def parse_supported_model_formats(cls, v):
        if isinstance(v, str):
            return [format_type.strip() for format_type in v.split(",")]
        return v
    
    @validator("allowed_file_extensions", pre=True)
    def parse_allowed_file_extensions(cls, v):
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()


# 导出常用配置
settings = get_settings()
